# List of SDK dependencies of this app, this information is also included in an encrypted form in the APK.
# For more information visit: https://d.android.com/r/tools/dependency-metadata

library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib"
    version: "1.9.20"
  }
  digests {
    sha256: "(\243[\315\377F\330d\370\0174ja~Hb\204\262\b\321sx\304\031\000\337\261\336\225\251\016l"
  }
}
library {
  maven_library {
    groupId: "org.jetbrains"
    artifactId: "annotations"
    version: "13.0"
  }
  digests {
    sha256: "\254\342\241\r\310\342\325\3754\222^\312\300>I\210\262\300\370Qe\f\224\270\316\364\233\241\275\021\024x"
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk8"
    version: "1.8.0"
  }
  digests {
    sha256: "\005\266(\004D\033\f\232\031 \266\267\325\317s)\244\342KbXG\2162\261\360F\312\001\220\tF"
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk7"
    version: "1.8.0"
  }
  digests {
    sha256: "L\210\235\035\230\003\365\362\353l\025\222\246\267\346#i\254v`\311\356\341Z\272\026\376\300Y\0266f"
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-common"
    version: "1.9.20"
  }
}
library {
  maven_library {
    groupId: "androidx.fragment"
    artifactId: "fragment"
    version: "1.7.1"
  }
  digests {
    sha256: "}\336\276\235\203\n\004\22673\334\263@\312\264\272\301\346\235\374\247\227\246\325\232\247\274V(!P\257"
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity"
    version: "1.8.1"
  }
  digests {
    sha256: "f\274,\023\275\221\373\216\240\236D[!\367\204\323\345\323\251\354\233\030\f\320~\016\222\b\302\3719\306"
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation"
    version: "1.2.0"
  }
  digests {
    sha256: "\220)&+\335\316\021nm\002\276I\236J\375\272!\362L#\220\207\267k;W\327\351\213I\n6"
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection"
    version: "1.1.0"
  }
  digests {
    sha256: "c*\016T\aF\035\347t@\223R\224\016)*)\0207rB\a\247\207\202\fw\332\367\323;r"
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core"
    version: "1.8.0"
  }
  digests {
    sha256: "H\306J\025\354>\261\033\37333\236\\\353p\354\177\202\033\322\337\242\353\206u\353\32553\027\347\222"
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-experimental"
    version: "1.4.0"
  }
  digests {
    sha256: "\306\353~g`\021\354e\263$(7=E\r\353\337\304Qy\304\370\263\247R\027O\270|\027\260\212"
  }
}
library {
  maven_library {
    groupId: "androidx.concurrent"
    artifactId: "concurrent-futures"
    version: "1.1.0"
  }
  digests {
    sha256: "\f\340g\305\024\240\321\004\235\033\353\337p\2364N\323&o\351tBuh)7\315\313\0233N\236"
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "listenablefuture"
    version: "1.0"
  }
  digests {
    sha256: "\344\255v\a\345\300G|o\211\016\362jI\313\215\033\264\337\373e\v\253E\002\257\356ddN0i"
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime"
    version: "2.6.1"
  }
  digests {
    sha256: "Hg\375Ryt/\272\203\210\202\0310\313*\377\340m\201\245(\024\347\344\036p9.\240\357\210|"
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-common"
    version: "2.2.0"
  }
  digests {
    sha256: "e0\212\006\261\300\016\341\206\313\236\0312\023\203\360C\271\223\201?\025\"\304\177J>3\003\275\272A"
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-runtime"
    version: "2.2.0"
  }
  digests {
    sha256: "\241\276^\f\252+\ab8b\257j\342\033:\260q\201#$Q\204\320\343\r\352\201\265?\231\nG"
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common"
    version: "2.6.1"
  }
  digests {
    sha256: "\363H1\266\307\034\330D\341\323]\033\344\235^yD|Z\270V4e1\261\350go\332st\261"
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-android"
    version: "1.6.4"
  }
  digests {
    sha256: "?\334\016\355[\304\270>\351b\'tR\n-\262Tp7\016\254\321X\034\254\0367pO\t[\000"
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core"
    version: "1.6.4"
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core-jvm"
    version: "1.6.4"
  }
  digests {
    sha256: "\302L\213\262{\263 \304\2518qP\032~^\fa`v8\220{\031z\357gU\023\324\310 \276"
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-bom"
    version: "1.6.4"
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata"
    version: "2.6.1"
  }
  digests {
    sha256: "g5\237`\235\374+\366]\241\'\v#\003?\205`d\354\'\237\005\216\np\307\025\367\311\00001"
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core"
    version: "2.6.1"
  }
  digests {
    sha256: "\"Vx\n<\377J\036W\373\263\324BU|\027\3346:\270\257\020[\312\365&\035\216-]\271I"
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel"
    version: "2.6.1"
  }
  digests {
    sha256: "\344\377C8\231\236\034l\234rG\031\365\324\252}\326\033\366\365E\325%j\'\251\323u\337\237#0"
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-savedstate"
    version: "2.6.1"
  }
  digests {
    sha256: "\310/\211\"\032\333\341\235\367\307\255\272\266?N\314\205\177\307F\343\311IBV\253\217\245\302\004\221\262"
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-ktx"
    version: "1.2.0"
  }
  digests {
    sha256: "\334\267MQ\rU+5\357\367;\r\322{\202\226IS_9\002\345\265\241\362`@8<\020\251@"
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate"
    version: "1.2.1"
  }
  digests {
    sha256: "!\247\324\274\366\275\271J\327\271(8\001R\223\000\264\373\270\200\214\244\361\221\340\315\316o\330\344pZ"
  }
}
library {
  maven_library {
    groupId: "androidx.profileinstaller"
    artifactId: "profileinstaller"
    version: "1.3.1"
  }
  digests {
    sha256: "\320\344\002\3541\362@(\241\334~\266\240\243\371\331c\\\024Y9,\32749cC\267=g9H"
  }
}
library {
  maven_library {
    groupId: "androidx.startup"
    artifactId: "startup-runtime"
    version: "1.1.1"
  }
  digests {
    sha256: "\340\2462\2327\022b\376LE\003r\267\017\332\363;v\236\366\221p\224r7\207\317\316\211k\035\323"
  }
}
library {
  maven_library {
    groupId: "androidx.tracing"
    artifactId: "tracing"
    version: "1.0.0"
  }
  digests {
    sha256: "\a\270\266\023\226e\270\204\241b\354\317\227\211\034\245\017\177V\203\0223\277%\026\212\340O{V\206\022"
  }
}
library {
  maven_library {
    groupId: "androidx.versionedparcelable"
    artifactId: "versionedparcelable"
    version: "1.1.1"
  }
  digests {
    sha256: "W\350\3312`\321\215[\220\a\311\356\323\306J\321Y\336\220\310`\236\277\307J4|\275QE5\244"
  }
}
library {
  maven_library {
    groupId: "androidx.loader"
    artifactId: "loader"
    version: "1.0.0"
  }
  digests {
    sha256: "\021\3675\313;U\304X\324p\276\331\342RT7[Q\213K\033\255i&x:p&\333\017P%"
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager"
    artifactId: "viewpager"
    version: "1.0.0"
  }
  digests {
    sha256: "\024z\364\341J\031\204\001\r\217\025^^\031\327\201\360<\035p\337\355\002\250\340\321\204(\270\374\206\202"
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview"
    version: "1.0.0"
  }
  digests {
    sha256: " \345\270\366Rj4YZ`OVq\215\250\021g\300\264\nz\224\245}\2525Vc\362YM\362"
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-splashscreen"
    version: "1.0.1"
  }
  digests {
    sha256: "%\310\023\256w\225\311\235\322\006(RpC\370\320B^\262\350&\346\304,\267z\313\266\300\035\336h"
  }
}
library {
  digests {
    sha256: "\017\314\376\032\255\344\000\026?\237j\000\354\2174SQiB\342\305_\3645\355\254\a\fh\221\303\377"
  }
}
library {
  digests {
    sha256: "\357\350\324Jw\225\276wH\035O\370?NH\212\211\005\262L!7\"\b78\300\324q\177\216["
  }
}
library_dependencies {
  library_dep_index: 1
  library_dep_index: 2
  library_dep_index: 3
  library_dep_index: 4
}
library_dependencies {
  library_index: 2
  library_dep_index: 0
  library_dep_index: 3
}
library_dependencies {
  library_index: 3
  library_dep_index: 0
}
library_dependencies {
  library_index: 4
  library_dep_index: 0
}
library_dependencies {
  library_index: 5
  library_dep_index: 6
  library_dep_index: 7
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 25
  library_dep_index: 22
  library_dep_index: 13
  library_dep_index: 23
  library_dep_index: 24
  library_dep_index: 31
  library_dep_index: 27
  library_dep_index: 26
  library_dep_index: 32
  library_dep_index: 0
}
library_dependencies {
  library_index: 6
  library_dep_index: 7
  library_dep_index: 8
  library_dep_index: 9
  library_dep_index: 13
  library_dep_index: 23
  library_dep_index: 24
  library_dep_index: 27
  library_dep_index: 26
  library_dep_index: 29
  library_dep_index: 0
}
library_dependencies {
  library_index: 8
  library_dep_index: 7
}
library_dependencies {
  library_index: 9
  library_dep_index: 7
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 11
  library_dep_index: 13
  library_dep_index: 30
}
library_dependencies {
  library_index: 10
  library_dep_index: 0
}
library_dependencies {
  library_index: 11
  library_dep_index: 7
  library_dep_index: 12
}
library_dependencies {
  library_index: 13
  library_dep_index: 7
  library_dep_index: 14
  library_dep_index: 15
  library_dep_index: 16
  library_dep_index: 27
  library_dep_index: 0
  library_dep_index: 16
  library_dep_index: 22
  library_dep_index: 23
  library_dep_index: 24
  library_dep_index: 21
}
library_dependencies {
  library_index: 14
  library_dep_index: 7
}
library_dependencies {
  library_index: 15
  library_dep_index: 7
  library_dep_index: 14
}
library_dependencies {
  library_index: 16
  library_dep_index: 7
  library_dep_index: 0
  library_dep_index: 17
  library_dep_index: 21
  library_dep_index: 22
  library_dep_index: 13
  library_dep_index: 23
  library_dep_index: 24
}
library_dependencies {
  library_index: 17
  library_dep_index: 18
  library_dep_index: 20
  library_dep_index: 2
}
library_dependencies {
  library_index: 18
  library_dep_index: 19
}
library_dependencies {
  library_index: 19
  library_dep_index: 20
  library_dep_index: 2
  library_dep_index: 4
}
library_dependencies {
  library_index: 20
  library_dep_index: 17
  library_dep_index: 18
  library_dep_index: 19
}
library_dependencies {
  library_index: 21
  library_dep_index: 14
  library_dep_index: 15
  library_dep_index: 22
  library_dep_index: 0
  library_dep_index: 16
  library_dep_index: 22
  library_dep_index: 13
  library_dep_index: 23
  library_dep_index: 24
}
library_dependencies {
  library_index: 22
  library_dep_index: 14
  library_dep_index: 15
  library_dep_index: 16
  library_dep_index: 0
  library_dep_index: 16
  library_dep_index: 13
  library_dep_index: 23
  library_dep_index: 24
  library_dep_index: 21
}
library_dependencies {
  library_index: 23
  library_dep_index: 7
  library_dep_index: 0
  library_dep_index: 16
  library_dep_index: 22
  library_dep_index: 13
  library_dep_index: 24
  library_dep_index: 21
}
library_dependencies {
  library_index: 24
  library_dep_index: 7
  library_dep_index: 25
  library_dep_index: 22
  library_dep_index: 23
  library_dep_index: 26
  library_dep_index: 0
  library_dep_index: 17
  library_dep_index: 16
  library_dep_index: 22
  library_dep_index: 13
  library_dep_index: 23
  library_dep_index: 21
}
library_dependencies {
  library_index: 25
  library_dep_index: 0
  library_dep_index: 7
  library_dep_index: 9
}
library_dependencies {
  library_index: 26
  library_dep_index: 7
  library_dep_index: 14
  library_dep_index: 16
  library_dep_index: 0
}
library_dependencies {
  library_index: 27
  library_dep_index: 7
  library_dep_index: 11
  library_dep_index: 28
  library_dep_index: 12
}
library_dependencies {
  library_index: 28
  library_dep_index: 7
  library_dep_index: 29
}
library_dependencies {
  library_index: 29
  library_dep_index: 7
}
library_dependencies {
  library_index: 30
  library_dep_index: 7
  library_dep_index: 8
}
library_dependencies {
  library_index: 31
  library_dep_index: 7
  library_dep_index: 9
  library_dep_index: 21
  library_dep_index: 23
}
library_dependencies {
  library_index: 32
  library_dep_index: 7
  library_dep_index: 9
  library_dep_index: 33
}
library_dependencies {
  library_index: 33
  library_dep_index: 7
  library_dep_index: 9
}
library_dependencies {
  library_index: 34
  library_dep_index: 7
  library_dep_index: 0
}
module_dependencies {
  module_name: "base"
  dependency_index: 0
  dependency_index: 5
  dependency_index: 34
  dependency_index: 35
  dependency_index: 36
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://plugins.gradle.org/m2"
  }
}
repositories {
  maven_repo {
    url: "https://plugins.gradle.org/m2/"
  }
}
repositories {
  maven_repo {
    url: "https://s01.oss.sonatype.org/content/repositories/snapshots/"
  }
}
