-- Merging decision tree log ---
meta-data#org.godotengine.editor.version
INJECTED from /Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:33:9-35:53
	android:value
		INJECTED from /Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml
meta-data#org.godotengine.rendering.method
INJECTED from /Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:37:9-39:54
	android:value
		INJECTED from /Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml
manifest
ADDED from /Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:2:1-62:12
MERGED from /Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:2:1-62:12
INJECTED from /Users/<USER>/Desktop/workspace/bingo/android/build/src/release/AndroidManifest.xml:2:1-35:12
INJECTED from /Users/<USER>/Desktop/workspace/bingo/android/build/src/release/AndroidManifest.xml:2:1-35:12
INJECTED from /Users/<USER>/Desktop/workspace/bingo/android/build/src/release/AndroidManifest.xml:2:1-35:12
MERGED from [godot-lib.template_release.aar] /Users/<USER>/.gradle/caches/transforms-3/d0bd9ad73902ada629479ce0a98d38a5/transformed/jetified-godot-lib.template_release/AndroidManifest.xml:2:1-35:12
MERGED from [androidx.fragment:fragment:1.7.1] /Users/<USER>/.gradle/caches/transforms-3/34ec294f6bdde40744063ea74c4e92c4/transformed/fragment-1.7.1/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-splashscreen:1.0.1] /Users/<USER>/.gradle/caches/transforms-3/dd89130c4a8ca50a04569d2aa68e9e1e/transformed/jetified-core-splashscreen-1.0.1/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.8.1] /Users/<USER>/.gradle/caches/transforms-3/c03fac50118fed4c446fe2dbbb7ccb60/transformed/jetified-activity-1.8.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/0e21b8b143a9eca812fc37f7cee0557f/transformed/loader-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/transforms-3/72bc74126f9cd461c513810c4d33efc5/transformed/jetified-savedstate-1.2.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/c96a8659546c6e7fe813d23b94cc24a7/transformed/lifecycle-livedata-2.6.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/29441c4b9970a8bec5ae4c6584bf1466/transformed/lifecycle-viewmodel-2.6.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/33c479b53b41603cc3192168771adda7/transformed/lifecycle-runtime-2.6.1/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/687008004bad3d3378837076129c7833/transformed/lifecycle-livedata-core-2.6.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/3b7b23c7cf9d64f2cdf97830e0eaf3fa/transformed/jetified-lifecycle-viewmodel-savedstate-2.6.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/dcd51112b48fdacc6fc8611b6f523186/transformed/jetified-core-ktx-1.2.0/AndroidManifest.xml:2:1-9:12
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/48a153a700b78e91284b2dca9fa78d0a/transformed/viewpager-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/ef75778674586b9b7c56110b80ffc4d3/transformed/customview-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.8.0] /Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/AndroidManifest.xml:17:1-26:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] /Users/<USER>/.gradle/caches/transforms-3/288a5c40ac7d8b5c4ea100b1f7d67859/transformed/jetified-annotation-experimental-1.4.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/93a030528cee287bd3c372bd0198e42f/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:17:1-27:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/618293b988a94b2ad64fc8615e872ffd/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/9e5db9b26d9392a98e8f1e9350d92494/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/5b4262798d487343ced4e8e41460cd2e/transformed/jetified-tracing-1.0.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/transforms-3/322ae571677f51e5eec52131b265a665/transformed/core-runtime-2.2.0/AndroidManifest.xml:17:1-22:12
	package
		INJECTED from /Users/<USER>/Desktop/workspace/bingo/android/build/src/release/AndroidManifest.xml
	android:versionName
		ADDED from /Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:5:5-30
		ADDED from /Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:5:5-30
		INJECTED from /Users/<USER>/Desktop/workspace/bingo/android/build/src/release/AndroidManifest.xml
	xmlns:tools
		ADDED from /Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:3:5-51
	xmlns:android
		ADDED from /Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:2:11-69
	android:versionCode
		ADDED from /Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:4:5-28
		ADDED from /Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:4:5-28
		INJECTED from /Users/<USER>/Desktop/workspace/bingo/android/build/src/release/AndroidManifest.xml
	android:installLocation
		ADDED from /Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:6:5-35
		ADDED from /Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:6:5-35
supports-screens
ADDED from /Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:8:5-12:40
REJECTED from /Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:8:5-12:40
	tools:node
		ADDED from /Users/<USER>/Desktop/workspace/bingo/android/build/src/release/AndroidManifest.xml:5:9-29
	android:largeScreens
		ADDED from /Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:11:9-36
	android:smallScreens
		ADDED from /Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:9:9-36
	android:normalScreens
		ADDED from /Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:10:9-37
	android:xlargeScreens
		ADDED from /Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:12:9-37
uses-feature#0x00030000
ADDED from /Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:14:5-16:35
MERGED from /Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:14:5-16:35
MERGED from /Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:14:5-16:35
	android:glEsVersion
		ADDED from /Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:15:9-41
	android:required
		ADDED from /Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:16:9-32
application
ADDED from /Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:18:5-60:19
MERGED from /Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:18:5-60:19
MERGED from /Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:18:5-60:19
INJECTED from /Users/<USER>/Desktop/workspace/bingo/android/build/src/release/AndroidManifest.xml:17:5-34:19
MERGED from [godot-lib.template_release.aar] /Users/<USER>/.gradle/caches/transforms-3/d0bd9ad73902ada629479ce0a98d38a5/transformed/jetified-godot-lib.template_release/AndroidManifest.xml:9:5-33:19
MERGED from [godot-lib.template_release.aar] /Users/<USER>/.gradle/caches/transforms-3/d0bd9ad73902ada629479ce0a98d38a5/transformed/jetified-godot-lib.template_release/AndroidManifest.xml:9:5-33:19
MERGED from [androidx.core:core:1.8.0] /Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/AndroidManifest.xml:24:5-89
MERGED from [androidx.core:core:1.8.0] /Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/AndroidManifest.xml:24:5-89
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/93a030528cee287bd3c372bd0198e42f/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/93a030528cee287bd3c372bd0198e42f/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/618293b988a94b2ad64fc8615e872ffd/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/618293b988a94b2ad64fc8615e872ffd/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/9e5db9b26d9392a98e8f1e9350d92494/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/9e5db9b26d9392a98e8f1e9350d92494/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from /Users/<USER>/Desktop/workspace/bingo/android/build/src/release/AndroidManifest.xml
	android:requestLegacyExternalStorage
		ADDED from /Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:25:9-53
		REJECTED from /Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:25:9-53
	android:appComponentFactory
		ADDED from [androidx.core:core:1.8.0] /Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/AndroidManifest.xml:24:18-86
	android:label
		ADDED from /Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:19:9-58
	android:appCategory
		ADDED from /Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:22:9-35
		REJECTED from /Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:22:9-35
	tools:ignore
		ADDED from /Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:26:9-48
	android:icon
		ADDED from /Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:21:9-36
	android:allowBackup
		ADDED from /Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:20:9-36
		REJECTED from /Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:20:9-36
	tools:replace
		ADDED from /Users/<USER>/Desktop/workspace/bingo/android/build/src/release/AndroidManifest.xml:24:9-143
	android:isGame
		ADDED from /Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:23:9-30
		REJECTED from /Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:23:9-30
	android:hasFragileUserData
		ADDED from /Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:24:9-43
		REJECTED from /Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:24:9-43
profileable
ADDED from /Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:27:9-30:36
	android:enabled
		ADDED from /Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:29:13-35
	android:shell
		ADDED from /Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:28:13-33
	tools:targetApi
		ADDED from /Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:30:13-33
activity#com.godot.game.GodotApp
ADDED from /Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:41:9-58:20
MERGED from /Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:41:9-58:20
MERGED from /Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:41:9-58:20
REJECTED from /Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:53:13-57:29
	tools:node
		ADDED from /Users/<USER>/Desktop/workspace/bingo/android/build/src/release/AndroidManifest.xml:27:140-172
	android:screenOrientation
		ADDED from /Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:48:13-50
		REJECTED from /Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:48:13-50
	android:label
		ADDED from /Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:43:13-62
		ADDED from /Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:43:13-62
	android:excludeFromRecents
		ADDED from /Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:46:13-47
		REJECTED from /Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:46:13-47
	android:launchMode
		ADDED from /Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:45:13-55
		ADDED from /Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:45:13-55
	android:exported
		ADDED from /Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:47:13-36
		ADDED from /Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:47:13-36
	android:resizeableActivity
		ADDED from /Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:50:13-47
		REJECTED from /Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:50:13-47
	tools:ignore
		ADDED from /Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:51:13-43
		ADDED from /Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:51:13-43
	android:configChanges
		ADDED from /Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:49:13-164
		ADDED from /Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:49:13-164
	android:theme
		ADDED from /Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:44:13-55
		ADDED from /Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:44:13-55
	tools:replace
		ADDED from /Users/<USER>/Desktop/workspace/bingo/android/build/src/release/AndroidManifest.xml:27:44-139
	android:name
		ADDED from /Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:42:13-37
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.DEFAULT+category:name:android.intent.category.LAUNCHER
ADDED from /Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:53:13-57:29
action#android.intent.action.MAIN
ADDED from /Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:54:17-69
	android:name
		ADDED from /Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:54:25-66
category#android.intent.category.DEFAULT
ADDED from /Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:55:17-76
	android:name
		ADDED from /Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:55:27-73
category#android.intent.category.LAUNCHER
ADDED from /Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:56:17-77
	android:name
		ADDED from /Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:56:27-74
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from /Users/<USER>/Desktop/workspace/bingo/android/build/src/release/AndroidManifest.xml:11:5-79
	android:name
		ADDED from /Users/<USER>/Desktop/workspace/bingo/android/build/src/release/AndroidManifest.xml:11:22-76
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from /Users/<USER>/Desktop/workspace/bingo/android/build/src/release/AndroidManifest.xml:12:5-76
	android:name
		ADDED from /Users/<USER>/Desktop/workspace/bingo/android/build/src/release/AndroidManifest.xml:12:22-73
uses-permission#android.permission.CHANGE_WIFI_MULTICAST_STATE
ADDED from /Users/<USER>/Desktop/workspace/bingo/android/build/src/release/AndroidManifest.xml:13:5-86
	android:name
		ADDED from /Users/<USER>/Desktop/workspace/bingo/android/build/src/release/AndroidManifest.xml:13:22-83
uses-permission#android.permission.INTERNET
ADDED from /Users/<USER>/Desktop/workspace/bingo/android/build/src/release/AndroidManifest.xml:14:5-67
	android:name
		ADDED from /Users/<USER>/Desktop/workspace/bingo/android/build/src/release/AndroidManifest.xml:14:22-64
uses-permission#android.permission.VIBRATE
ADDED from /Users/<USER>/Desktop/workspace/bingo/android/build/src/release/AndroidManifest.xml:15:5-66
	android:name
		ADDED from /Users/<USER>/Desktop/workspace/bingo/android/build/src/release/AndroidManifest.xml:15:22-63
uses-permission#android.permission.WAKE_LOCK
ADDED from /Users/<USER>/Desktop/workspace/bingo/android/build/src/release/AndroidManifest.xml:16:5-68
	android:name
		ADDED from /Users/<USER>/Desktop/workspace/bingo/android/build/src/release/AndroidManifest.xml:16:22-65
uses-sdk
INJECTED from /Users/<USER>/Desktop/workspace/bingo/android/build/src/release/AndroidManifest.xml reason: use-sdk injection requested
INJECTED from /Users/<USER>/Desktop/workspace/bingo/android/build/src/release/AndroidManifest.xml
INJECTED from /Users/<USER>/Desktop/workspace/bingo/android/build/src/release/AndroidManifest.xml
MERGED from [godot-lib.template_release.aar] /Users/<USER>/.gradle/caches/transforms-3/d0bd9ad73902ada629479ce0a98d38a5/transformed/jetified-godot-lib.template_release/AndroidManifest.xml:7:5-44
MERGED from [godot-lib.template_release.aar] /Users/<USER>/.gradle/caches/transforms-3/d0bd9ad73902ada629479ce0a98d38a5/transformed/jetified-godot-lib.template_release/AndroidManifest.xml:7:5-44
MERGED from [androidx.fragment:fragment:1.7.1] /Users/<USER>/.gradle/caches/transforms-3/34ec294f6bdde40744063ea74c4e92c4/transformed/fragment-1.7.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.7.1] /Users/<USER>/.gradle/caches/transforms-3/34ec294f6bdde40744063ea74c4e92c4/transformed/fragment-1.7.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-splashscreen:1.0.1] /Users/<USER>/.gradle/caches/transforms-3/dd89130c4a8ca50a04569d2aa68e9e1e/transformed/jetified-core-splashscreen-1.0.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core-splashscreen:1.0.1] /Users/<USER>/.gradle/caches/transforms-3/dd89130c4a8ca50a04569d2aa68e9e1e/transformed/jetified-core-splashscreen-1.0.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.8.1] /Users/<USER>/.gradle/caches/transforms-3/c03fac50118fed4c446fe2dbbb7ccb60/transformed/jetified-activity-1.8.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.1] /Users/<USER>/.gradle/caches/transforms-3/c03fac50118fed4c446fe2dbbb7ccb60/transformed/jetified-activity-1.8.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/0e21b8b143a9eca812fc37f7cee0557f/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/0e21b8b143a9eca812fc37f7cee0557f/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/transforms-3/72bc74126f9cd461c513810c4d33efc5/transformed/jetified-savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/transforms-3/72bc74126f9cd461c513810c4d33efc5/transformed/jetified-savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/c96a8659546c6e7fe813d23b94cc24a7/transformed/lifecycle-livedata-2.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/c96a8659546c6e7fe813d23b94cc24a7/transformed/lifecycle-livedata-2.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/29441c4b9970a8bec5ae4c6584bf1466/transformed/lifecycle-viewmodel-2.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/29441c4b9970a8bec5ae4c6584bf1466/transformed/lifecycle-viewmodel-2.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/33c479b53b41603cc3192168771adda7/transformed/lifecycle-runtime-2.6.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/33c479b53b41603cc3192168771adda7/transformed/lifecycle-runtime-2.6.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/687008004bad3d3378837076129c7833/transformed/lifecycle-livedata-core-2.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/687008004bad3d3378837076129c7833/transformed/lifecycle-livedata-core-2.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/3b7b23c7cf9d64f2cdf97830e0eaf3fa/transformed/jetified-lifecycle-viewmodel-savedstate-2.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/3b7b23c7cf9d64f2cdf97830e0eaf3fa/transformed/jetified-lifecycle-viewmodel-savedstate-2.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/dcd51112b48fdacc6fc8611b6f523186/transformed/jetified-core-ktx-1.2.0/AndroidManifest.xml:5:5-7:41
MERGED from [androidx.core:core-ktx:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/dcd51112b48fdacc6fc8611b6f523186/transformed/jetified-core-ktx-1.2.0/AndroidManifest.xml:5:5-7:41
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/48a153a700b78e91284b2dca9fa78d0a/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/48a153a700b78e91284b2dca9fa78d0a/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/ef75778674586b9b7c56110b80ffc4d3/transformed/customview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/ef75778674586b9b7c56110b80ffc4d3/transformed/customview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.8.0] /Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.8.0] /Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.annotation:annotation-experimental:1.4.0] /Users/<USER>/.gradle/caches/transforms-3/288a5c40ac7d8b5c4ea100b1f7d67859/transformed/jetified-annotation-experimental-1.4.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] /Users/<USER>/.gradle/caches/transforms-3/288a5c40ac7d8b5c4ea100b1f7d67859/transformed/jetified-annotation-experimental-1.4.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/93a030528cee287bd3c372bd0198e42f/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/93a030528cee287bd3c372bd0198e42f/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/618293b988a94b2ad64fc8615e872ffd/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/618293b988a94b2ad64fc8615e872ffd/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/9e5db9b26d9392a98e8f1e9350d92494/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/9e5db9b26d9392a98e8f1e9350d92494/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/5b4262798d487343ced4e8e41460cd2e/transformed/jetified-tracing-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/5b4262798d487343ced4e8e41460cd2e/transformed/jetified-tracing-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/transforms-3/322ae571677f51e5eec52131b265a665/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/transforms-3/322ae571677f51e5eec52131b265a665/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
	android:targetSdkVersion
		INJECTED from /Users/<USER>/Desktop/workspace/bingo/android/build/src/release/AndroidManifest.xml
	android:minSdkVersion
		INJECTED from /Users/<USER>/Desktop/workspace/bingo/android/build/src/release/AndroidManifest.xml
meta-data#org.godotengine.library.version
ADDED from [godot-lib.template_release.aar] /Users/<USER>/.gradle/caches/transforms-3/d0bd9ad73902ada629479ce0a98d38a5/transformed/jetified-godot-lib.template_release/AndroidManifest.xml:12:9-14:44
	android:value
		ADDED from [godot-lib.template_release.aar] /Users/<USER>/.gradle/caches/transforms-3/d0bd9ad73902ada629479ce0a98d38a5/transformed/jetified-godot-lib.template_release/AndroidManifest.xml:14:13-41
	android:name
		ADDED from [godot-lib.template_release.aar] /Users/<USER>/.gradle/caches/transforms-3/d0bd9ad73902ada629479ce0a98d38a5/transformed/jetified-godot-lib.template_release/AndroidManifest.xml:13:13-59
service#org.godotengine.godot.GodotDownloaderService
ADDED from [godot-lib.template_release.aar] /Users/<USER>/.gradle/caches/transforms-3/d0bd9ad73902ada629479ce0a98d38a5/transformed/jetified-godot-lib.template_release/AndroidManifest.xml:16:9-80
	android:name
		ADDED from [godot-lib.template_release.aar] /Users/<USER>/.gradle/caches/transforms-3/d0bd9ad73902ada629479ce0a98d38a5/transformed/jetified-godot-lib.template_release/AndroidManifest.xml:16:18-77
activity#org.godotengine.godot.utils.ProcessPhoenix
ADDED from [godot-lib.template_release.aar] /Users/<USER>/.gradle/caches/transforms-3/d0bd9ad73902ada629479ce0a98d38a5/transformed/jetified-godot-lib.template_release/AndroidManifest.xml:18:9-22:75
	android:process
		ADDED from [godot-lib.template_release.aar] /Users/<USER>/.gradle/caches/transforms-3/d0bd9ad73902ada629479ce0a98d38a5/transformed/jetified-godot-lib.template_release/AndroidManifest.xml:21:13-39
	android:exported
		ADDED from [godot-lib.template_release.aar] /Users/<USER>/.gradle/caches/transforms-3/d0bd9ad73902ada629479ce0a98d38a5/transformed/jetified-godot-lib.template_release/AndroidManifest.xml:20:13-37
	android:theme
		ADDED from [godot-lib.template_release.aar] /Users/<USER>/.gradle/caches/transforms-3/d0bd9ad73902ada629479ce0a98d38a5/transformed/jetified-godot-lib.template_release/AndroidManifest.xml:22:13-72
	android:name
		ADDED from [godot-lib.template_release.aar] /Users/<USER>/.gradle/caches/transforms-3/d0bd9ad73902ada629479ce0a98d38a5/transformed/jetified-godot-lib.template_release/AndroidManifest.xml:19:13-70
provider#androidx.core.content.FileProvider
ADDED from [godot-lib.template_release.aar] /Users/<USER>/.gradle/caches/transforms-3/d0bd9ad73902ada629479ce0a98d38a5/transformed/jetified-godot-lib.template_release/AndroidManifest.xml:24:9-32:20
	android:grantUriPermissions
		ADDED from [godot-lib.template_release.aar] /Users/<USER>/.gradle/caches/transforms-3/d0bd9ad73902ada629479ce0a98d38a5/transformed/jetified-godot-lib.template_release/AndroidManifest.xml:28:13-47
	android:authorities
		ADDED from [godot-lib.template_release.aar] /Users/<USER>/.gradle/caches/transforms-3/d0bd9ad73902ada629479ce0a98d38a5/transformed/jetified-godot-lib.template_release/AndroidManifest.xml:26:13-64
	android:exported
		ADDED from [godot-lib.template_release.aar] /Users/<USER>/.gradle/caches/transforms-3/d0bd9ad73902ada629479ce0a98d38a5/transformed/jetified-godot-lib.template_release/AndroidManifest.xml:27:13-37
	android:name
		ADDED from [godot-lib.template_release.aar] /Users/<USER>/.gradle/caches/transforms-3/d0bd9ad73902ada629479ce0a98d38a5/transformed/jetified-godot-lib.template_release/AndroidManifest.xml:25:13-62
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [godot-lib.template_release.aar] /Users/<USER>/.gradle/caches/transforms-3/d0bd9ad73902ada629479ce0a98d38a5/transformed/jetified-godot-lib.template_release/AndroidManifest.xml:29:13-31:64
	android:resource
		ADDED from [godot-lib.template_release.aar] /Users/<USER>/.gradle/caches/transforms-3/d0bd9ad73902ada629479ce0a98d38a5/transformed/jetified-godot-lib.template_release/AndroidManifest.xml:31:17-61
	android:name
		ADDED from [godot-lib.template_release.aar] /Users/<USER>/.gradle/caches/transforms-3/d0bd9ad73902ada629479ce0a98d38a5/transformed/jetified-godot-lib.template_release/AndroidManifest.xml:30:17-67
provider#androidx.startup.InitializationProvider
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/618293b988a94b2ad64fc8615e872ffd/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/9e5db9b26d9392a98e8f1e9350d92494/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/9e5db9b26d9392a98e8f1e9350d92494/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/618293b988a94b2ad64fc8615e872ffd/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/618293b988a94b2ad64fc8615e872ffd/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/618293b988a94b2ad64fc8615e872ffd/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/618293b988a94b2ad64fc8615e872ffd/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:25:13-67
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/618293b988a94b2ad64fc8615e872ffd/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/618293b988a94b2ad64fc8615e872ffd/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/618293b988a94b2ad64fc8615e872ffd/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/618293b988a94b2ad64fc8615e872ffd/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/618293b988a94b2ad64fc8615e872ffd/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/618293b988a94b2ad64fc8615e872ffd/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/618293b988a94b2ad64fc8615e872ffd/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/618293b988a94b2ad64fc8615e872ffd/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/618293b988a94b2ad64fc8615e872ffd/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/618293b988a94b2ad64fc8615e872ffd/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/618293b988a94b2ad64fc8615e872ffd/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/618293b988a94b2ad64fc8615e872ffd/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/618293b988a94b2ad64fc8615e872ffd/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/618293b988a94b2ad64fc8615e872ffd/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/618293b988a94b2ad64fc8615e872ffd/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/618293b988a94b2ad64fc8615e872ffd/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/618293b988a94b2ad64fc8615e872ffd/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/618293b988a94b2ad64fc8615e872ffd/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/618293b988a94b2ad64fc8615e872ffd/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/618293b988a94b2ad64fc8615e872ffd/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/618293b988a94b2ad64fc8615e872ffd/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:50:25-92
