<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/workspace/bingo/android/build/src/main/jniLibs"/></dataSet><dataSet config="mono" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/workspace/bingo/android/build/src/mono/jniLibs"/></dataSet><dataSet config="release" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/workspace/bingo/android/build/libs/release"><file name="godot-lib.template_release.aar" path="/Users/<USER>/Desktop/workspace/bingo/android/build/libs/release/godot-lib.template_release.aar"/><file name="arm64-v8a/libSystem.Native.so" path="/Users/<USER>/Desktop/workspace/bingo/android/build/libs/release/arm64-v8a/libSystem.Native.so"/><file name="arm64-v8a/libmono-component-debugger-stub-static.a" path="/Users/<USER>/Desktop/workspace/bingo/android/build/libs/release/arm64-v8a/libmono-component-debugger-stub-static.a"/><file name="arm64-v8a/libSystem.Security.Cryptography.Native.Android.so" path="/Users/<USER>/Desktop/workspace/bingo/android/build/libs/release/arm64-v8a/libSystem.Security.Cryptography.Native.Android.so"/><file name="arm64-v8a/libmono-component-diagnostics_tracing.so" path="/Users/<USER>/Desktop/workspace/bingo/android/build/libs/release/arm64-v8a/libmono-component-diagnostics_tracing.so"/><file name="arm64-v8a/libmonosgen-2.0.so" path="/Users/<USER>/Desktop/workspace/bingo/android/build/libs/release/arm64-v8a/libmonosgen-2.0.so"/><file name="arm64-v8a/libmono-component-hot_reload.so" path="/Users/<USER>/Desktop/workspace/bingo/android/build/libs/release/arm64-v8a/libmono-component-hot_reload.so"/><file name="arm64-v8a/libSystem.Security.Cryptography.Native.Android.a" path="/Users/<USER>/Desktop/workspace/bingo/android/build/libs/release/arm64-v8a/libSystem.Security.Cryptography.Native.Android.a"/><file name="arm64-v8a/libmono-component-diagnostics_tracing-stub-static.a" path="/Users/<USER>/Desktop/workspace/bingo/android/build/libs/release/arm64-v8a/libmono-component-diagnostics_tracing-stub-static.a"/><file name="arm64-v8a/libSystem.Security.Cryptography.Native.Android.jar" path="/Users/<USER>/Desktop/workspace/bingo/android/build/libs/release/arm64-v8a/libSystem.Security.Cryptography.Native.Android.jar"/><file name="arm64-v8a/libmono-component-hot_reload-stub-static.a" path="/Users/<USER>/Desktop/workspace/bingo/android/build/libs/release/arm64-v8a/libmono-component-hot_reload-stub-static.a"/><file name="arm64-v8a/libSystem.IO.Compression.Native.so" path="/Users/<USER>/Desktop/workspace/bingo/android/build/libs/release/arm64-v8a/libSystem.IO.Compression.Native.so"/><file name="arm64-v8a/libmonosgen-2.0.a" path="/Users/<USER>/Desktop/workspace/bingo/android/build/libs/release/arm64-v8a/libmonosgen-2.0.a"/><file name="arm64-v8a/libmono-component-hot_reload-static.a" path="/Users/<USER>/Desktop/workspace/bingo/android/build/libs/release/arm64-v8a/libmono-component-hot_reload-static.a"/><file name="arm64-v8a/libmono-component-debugger.so" path="/Users/<USER>/Desktop/workspace/bingo/android/build/libs/release/arm64-v8a/libmono-component-debugger.so"/><file name="arm64-v8a/libSystem.Security.Cryptography.Native.Android.dex" path="/Users/<USER>/Desktop/workspace/bingo/android/build/libs/release/arm64-v8a/libSystem.Security.Cryptography.Native.Android.dex"/><file name="arm64-v8a/libmono-component-marshal-ilgen.so" path="/Users/<USER>/Desktop/workspace/bingo/android/build/libs/release/arm64-v8a/libmono-component-marshal-ilgen.so"/><file name="arm64-v8a/libmono-component-marshal-ilgen-stub-static.a" path="/Users/<USER>/Desktop/workspace/bingo/android/build/libs/release/arm64-v8a/libmono-component-marshal-ilgen-stub-static.a"/><file name="arm64-v8a/libSystem.IO.Compression.Native.a" path="/Users/<USER>/Desktop/workspace/bingo/android/build/libs/release/arm64-v8a/libSystem.IO.Compression.Native.a"/><file name="arm64-v8a/libSystem.Globalization.Native.a" path="/Users/<USER>/Desktop/workspace/bingo/android/build/libs/release/arm64-v8a/libSystem.Globalization.Native.a"/><file name="arm64-v8a/libSystem.Globalization.Native.so" path="/Users/<USER>/Desktop/workspace/bingo/android/build/libs/release/arm64-v8a/libSystem.Globalization.Native.so"/><file name="arm64-v8a/libmono-component-marshal-ilgen-static.a" path="/Users/<USER>/Desktop/workspace/bingo/android/build/libs/release/arm64-v8a/libmono-component-marshal-ilgen-static.a"/><file name="arm64-v8a/libSystem.Native.a" path="/Users/<USER>/Desktop/workspace/bingo/android/build/libs/release/arm64-v8a/libSystem.Native.a"/><file name="arm64-v8a/libmono-component-diagnostics_tracing-static.a" path="/Users/<USER>/Desktop/workspace/bingo/android/build/libs/release/arm64-v8a/libmono-component-diagnostics_tracing-static.a"/><file name="arm64-v8a/libmono-component-debugger-static.a" path="/Users/<USER>/Desktop/workspace/bingo/android/build/libs/release/arm64-v8a/libmono-component-debugger-static.a"/></source></dataSet><dataSet config="variant" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/workspace/bingo/android/build/src/monoRelease/jniLibs"/></dataSet></merger>