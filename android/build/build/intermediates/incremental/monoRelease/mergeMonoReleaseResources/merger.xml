<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="androidx.arch.core:core-runtime:2.2.0$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/.gradle/caches/transforms-3/322ae571677f51e5eec52131b265a665/transformed/core-runtime-2.2.0/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="androidx.arch.core:core-runtime:2.2.0" from-dependency="true" generated-set="androidx.arch.core:core-runtime:2.2.0$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/.gradle/caches/transforms-3/322ae571677f51e5eec52131b265a665/transformed/core-runtime-2.2.0/res"><file path="/Users/<USER>/.gradle/caches/transforms-3/322ae571677f51e5eec52131b265a665/transformed/core-runtime-2.2.0/res/values/values.xml" qualifiers=""/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="androidx.startup:startup-runtime:1.1.1$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/.gradle/caches/transforms-3/9e5db9b26d9392a98e8f1e9350d92494/transformed/jetified-startup-runtime-1.1.1/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="androidx.startup:startup-runtime:1.1.1" from-dependency="true" generated-set="androidx.startup:startup-runtime:1.1.1$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/.gradle/caches/transforms-3/9e5db9b26d9392a98e8f1e9350d92494/transformed/jetified-startup-runtime-1.1.1/res"><file path="/Users/<USER>/.gradle/caches/transforms-3/9e5db9b26d9392a98e8f1e9350d92494/transformed/jetified-startup-runtime-1.1.1/res/values/values.xml" qualifiers=""><string name="androidx_startup" translatable="false">androidx.startup</string></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="androidx.profileinstaller:profileinstaller:1.3.1$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/.gradle/caches/transforms-3/618293b988a94b2ad64fc8615e872ffd/transformed/jetified-profileinstaller-1.3.1/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="androidx.profileinstaller:profileinstaller:1.3.1" from-dependency="true" generated-set="androidx.profileinstaller:profileinstaller:1.3.1$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/.gradle/caches/transforms-3/618293b988a94b2ad64fc8615e872ffd/transformed/jetified-profileinstaller-1.3.1/res"><file path="/Users/<USER>/.gradle/caches/transforms-3/618293b988a94b2ad64fc8615e872ffd/transformed/jetified-profileinstaller-1.3.1/res/values/values.xml" qualifiers=""/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="androidx.annotation:annotation-experimental:1.4.0$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/.gradle/caches/transforms-3/288a5c40ac7d8b5c4ea100b1f7d67859/transformed/jetified-annotation-experimental-1.4.0/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="androidx.annotation:annotation-experimental:1.4.0" from-dependency="true" generated-set="androidx.annotation:annotation-experimental:1.4.0$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/.gradle/caches/transforms-3/288a5c40ac7d8b5c4ea100b1f7d67859/transformed/jetified-annotation-experimental-1.4.0/res"><file path="/Users/<USER>/.gradle/caches/transforms-3/288a5c40ac7d8b5c4ea100b1f7d67859/transformed/jetified-annotation-experimental-1.4.0/res/values/values.xml" qualifiers=""/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="androidx.core:core:1.8.0$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="androidx.core:core:1.8.0" from-dependency="true" generated-set="androidx.core:core:1.8.0$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res"><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-v21/values-v21.xml" qualifiers="v21"><color name="notification_action_color_filter">@color/androidx_core_secondary_text_default_material_light</color><dimen name="notification_content_margin_start">0dp</dimen><dimen name="notification_main_column_padding_top">0dp</dimen><dimen name="notification_media_narrow_margin">12dp</dimen><style name="TextAppearance.Compat.Notification" parent="@android:style/TextAppearance.Material.Notification"/><style name="TextAppearance.Compat.Notification.Info" parent="@android:style/TextAppearance.Material.Notification.Info"/><style name="TextAppearance.Compat.Notification.Time" parent="@android:style/TextAppearance.Material.Notification.Time"/><style name="TextAppearance.Compat.Notification.Title" parent="@android:style/TextAppearance.Material.Notification.Title"/><style name="Widget.Compat.NotificationActionContainer" parent="">
        <item name="android:background">@drawable/notification_action_background</item>
    </style><style name="Widget.Compat.NotificationActionText" parent="">
        <item name="android:textAppearance">?android:attr/textAppearanceButton</item>
        <item name="android:textColor">@color/androidx_core_secondary_text_default_material_light</item>
        <item name="android:textSize">@dimen/notification_action_text_size</item>
    </style></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-ro/values-ro.xml" qualifiers="ro"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-te/values-te.xml" qualifiers="te"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-ru/values-ru.xml" qualifiers="ru"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">">999"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-tl/values-tl.xml" qualifiers="tl"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-v16/values-v16.xml" qualifiers="v16"><dimen name="notification_right_side_padding_top">4dp</dimen></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-zh-rTW/values-zh-rTW.xml" qualifiers="zh-rTW"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-it/values-it.xml" qualifiers="it"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-ca/values-ca.xml" qualifiers="ca"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-is/values-is.xml" qualifiers="is"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-cs/values-cs.xml" qualifiers="cs"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-zh-rCN/values-zh-rCN.xml" qualifiers="zh-rCN"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-in/values-in.xml" qualifiers="in"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-ja/values-ja.xml" qualifiers="ja"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-el/values-el.xml" qualifiers="el"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-lv/values-lv.xml" qualifiers="lv"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-da/values-da.xml" qualifiers="da"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-mr/values-mr.xml" qualifiers="mr"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"९९९+"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-kk/values-kk.xml" qualifiers="kk"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-ky/values-ky.xml" qualifiers="ky"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-gu/values-gu.xml" qualifiers="gu"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-en-rCA/values-en-rCA.xml" qualifiers="en-rCA"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-mn/values-mn.xml" qualifiers="mn"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-en-rIN/values-en-rIN.xml" qualifiers="en-rIN"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-ms/values-ms.xml" qualifiers="ms"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-zh-rHK/values-zh-rHK.xml" qualifiers="zh-rHK"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-km/values-km.xml" qualifiers="km"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-pt-rBR/values-pt-rBR.xml" qualifiers="pt-rBR"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-hy/values-hy.xml" qualifiers="hy"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-am/values-am.xml" qualifiers="am"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-be/values-be.xml" qualifiers="be"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values/values.xml" qualifiers=""><attr format="reference" name="nestedScrollViewStyle"/><color name="androidx_core_ripple_material_light">#1f000000</color><color name="androidx_core_secondary_text_default_material_light">#8a000000</color><color name="notification_action_color_filter">#ffffffff</color><color name="notification_icon_bg_color">#ff9e9e9e</color><dimen name="compat_button_inset_horizontal_material">4dp</dimen><dimen name="compat_button_inset_vertical_material">6dp</dimen><dimen name="compat_button_padding_horizontal_material">8dp</dimen><dimen name="compat_button_padding_vertical_material">4dp</dimen><dimen name="compat_control_corner_material">2dp</dimen><dimen name="compat_notification_large_icon_max_height">320dp</dimen><dimen name="compat_notification_large_icon_max_width">320dp</dimen><dimen name="notification_action_icon_size">32dp</dimen><dimen name="notification_action_text_size">13sp</dimen><dimen name="notification_big_circle_margin">12dp</dimen><dimen name="notification_content_margin_start">8dp</dimen><dimen name="notification_large_icon_height">64dp</dimen><dimen name="notification_large_icon_width">64dp</dimen><dimen name="notification_main_column_padding_top">10dp</dimen><dimen name="notification_media_narrow_margin">@dimen/notification_content_margin_start</dimen><dimen name="notification_right_icon_size">16dp</dimen><dimen name="notification_right_side_padding_top">2dp</dimen><dimen name="notification_small_icon_background_padding">3dp</dimen><dimen name="notification_small_icon_size_as_large">24dp</dimen><dimen name="notification_subtext_size">13sp</dimen><dimen name="notification_top_pad">10dp</dimen><dimen name="notification_top_pad_large_text">5dp</dimen><drawable name="notification_template_icon_bg">#3333B5E5</drawable><drawable name="notification_template_icon_low_bg">#0cffffff</drawable><item name="accessibility_action_clickable_span" type="id"/><item name="accessibility_custom_action_0" type="id"/><item name="accessibility_custom_action_1" type="id"/><item name="accessibility_custom_action_10" type="id"/><item name="accessibility_custom_action_11" type="id"/><item name="accessibility_custom_action_12" type="id"/><item name="accessibility_custom_action_13" type="id"/><item name="accessibility_custom_action_14" type="id"/><item name="accessibility_custom_action_15" type="id"/><item name="accessibility_custom_action_16" type="id"/><item name="accessibility_custom_action_17" type="id"/><item name="accessibility_custom_action_18" type="id"/><item name="accessibility_custom_action_19" type="id"/><item name="accessibility_custom_action_2" type="id"/><item name="accessibility_custom_action_20" type="id"/><item name="accessibility_custom_action_21" type="id"/><item name="accessibility_custom_action_22" type="id"/><item name="accessibility_custom_action_23" type="id"/><item name="accessibility_custom_action_24" type="id"/><item name="accessibility_custom_action_25" type="id"/><item name="accessibility_custom_action_26" type="id"/><item name="accessibility_custom_action_27" type="id"/><item name="accessibility_custom_action_28" type="id"/><item name="accessibility_custom_action_29" type="id"/><item name="accessibility_custom_action_3" type="id"/><item name="accessibility_custom_action_30" type="id"/><item name="accessibility_custom_action_31" type="id"/><item name="accessibility_custom_action_4" type="id"/><item name="accessibility_custom_action_5" type="id"/><item name="accessibility_custom_action_6" type="id"/><item name="accessibility_custom_action_7" type="id"/><item name="accessibility_custom_action_8" type="id"/><item name="accessibility_custom_action_9" type="id"/><item name="line1" type="id"/><item name="line3" type="id"/><item name="tag_accessibility_actions" type="id"/><item name="tag_accessibility_clickable_spans" type="id"/><item name="tag_accessibility_heading" type="id"/><item name="tag_accessibility_pane_title" type="id"/><item name="tag_on_apply_window_listener" type="id"/><item name="tag_on_receive_content_listener" type="id"/><item name="tag_on_receive_content_mime_types" type="id"/><item name="tag_screen_reader_focusable" type="id"/><item name="tag_state_description" type="id"/><item name="tag_transition_group" type="id"/><item name="tag_unhandled_key_event_manager" type="id"/><item name="tag_unhandled_key_listeners" type="id"/><item name="tag_window_insets_animation_callback" type="id"/><item name="text" type="id"/><item name="text2" type="id"/><item name="title" type="id"/><integer name="status_bar_notification_info_maxnum">999</integer><string name="status_bar_notification_info_overflow">999+</string><style name="TextAppearance.Compat.Notification" parent="@android:style/TextAppearance.StatusBar.EventContent"/><style name="TextAppearance.Compat.Notification.Info">
        <item name="android:textSize">12sp</item>
        <item name="android:textColor">?android:attr/textColorSecondary</item>
    </style><style name="TextAppearance.Compat.Notification.Line2" parent="TextAppearance.Compat.Notification.Info"/><style name="TextAppearance.Compat.Notification.Time">
        <item name="android:textSize">12sp</item>
        <item name="android:textColor">?android:attr/textColorSecondary</item>
    </style><style name="TextAppearance.Compat.Notification.Title" parent="@android:style/TextAppearance.StatusBar.EventContent.Title"/><style name="Widget.Compat.NotificationActionContainer" parent=""/><style name="Widget.Compat.NotificationActionText" parent=""/><declare-styleable name="Capability">
        
        <attr format="reference" name="queryPatterns"/>
        
        <attr format="boolean" name="shortcutMatchRequired"/>
    </declare-styleable><declare-styleable name="ColorStateListItem">
        
        <attr name="android:color"/>
        
        <attr format="float" name="alpha"/>
        <attr name="android:alpha"/>
        
        <attr format="float" name="lStar"/>
        <attr name="android:lStar"/>
    </declare-styleable><declare-styleable name="FontFamily">
        
        <attr format="string" name="fontProviderAuthority"/>
        
        <attr format="string" name="fontProviderPackage"/>
        
        <attr format="string" name="fontProviderQuery"/>
        
        <attr format="reference" name="fontProviderCerts"/>
        
        <attr name="fontProviderFetchStrategy">
            
            <enum name="blocking" value="0"/>
            
            <enum name="async" value="1"/>
        </attr>
        
        <attr format="integer" name="fontProviderFetchTimeout">
            
            <enum name="forever" value="-1"/>
        </attr>
        
        <attr format="string" name="fontProviderSystemFontFamily"/>
    </declare-styleable><declare-styleable name="FontFamilyFont">
        
        <attr name="fontStyle">
            <enum name="normal" value="0"/>
            <enum name="italic" value="1"/>
        </attr>
        
        <attr format="reference" name="font"/>
        
        <attr format="integer" name="fontWeight"/>
        
        <attr format="string" name="fontVariationSettings"/>
        
        <attr format="integer" name="ttcIndex"/>
        
        <attr name="android:fontStyle"/>
        <attr name="android:font"/>
        <attr name="android:fontWeight"/>
        <attr name="android:fontVariationSettings"/>
        <attr name="android:ttcIndex"/>
    </declare-styleable><declare-styleable name="GradientColor">
        
        <attr name="android:startColor"/>
        
        <attr name="android:centerColor"/>
        
        <attr name="android:endColor"/>
        
        <attr name="android:type"/>

        
        
        <attr name="android:gradientRadius"/>

        
        
        <attr name="android:centerX"/>
        
        <attr name="android:centerY"/>

        
        
        <attr name="android:startX"/>
        
        <attr name="android:startY"/>
        
        <attr name="android:endX"/>
        
        <attr name="android:endY"/>

        
        <attr name="android:tileMode"/>
    </declare-styleable><declare-styleable name="GradientColorItem">
        
        <attr name="android:offset"/>
        
        <attr name="android:color"/>
    </declare-styleable></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-uz/values-uz.xml" qualifiers="uz"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-fr-rCA/values-fr-rCA.xml" qualifiers="fr-rCA"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-pl/values-pl.xml" qualifiers="pl"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-vi/values-vi.xml" qualifiers="vi"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-sq/values-sq.xml" qualifiers="sq"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-sv/values-sv.xml" qualifiers="sv"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-sl/values-sl.xml" qualifiers="sl"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-sk/values-sk.xml" qualifiers="sk"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-ur/values-ur.xml" qualifiers="ur"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"+999"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-sw/values-sw.xml" qualifiers="sw"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-pt-rPT/values-pt-rPT.xml" qualifiers="pt-rPT"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-tr/values-tr.xml" qualifiers="tr"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-ta/values-ta.xml" qualifiers="ta"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-th/values-th.xml" qualifiers="th"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-fa/values-fa.xml" qualifiers="fa"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-lt/values-lt.xml" qualifiers="lt"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-or/values-or.xml" qualifiers="or"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-eu/values-eu.xml" qualifiers="eu"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-lo/values-lo.xml" qualifiers="lo"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-iw/values-iw.xml" qualifiers="iw"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-en-rGB/values-en-rGB.xml" qualifiers="en-rGB"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-fi/values-fi.xml" qualifiers="fi"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-en-rAU/values-en-rAU.xml" qualifiers="en-rAU"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-fr/values-fr.xml" qualifiers="fr"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-es/values-es.xml" qualifiers="es"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-et/values-et.xml" qualifiers="et"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-hr/values-hr.xml" qualifiers="hr"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-hu/values-hu.xml" qualifiers="hu"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-nl/values-nl.xml" qualifiers="nl"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-bg/values-bg.xml" qualifiers="bg"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-bn/values-bn.xml" qualifiers="bn"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"৯৯৯+"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-ne/values-ne.xml" qualifiers="ne"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"९९९+"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-af/values-af.xml" qualifiers="af"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-nb/values-nb.xml" qualifiers="nb"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-hi/values-hi.xml" qualifiers="hi"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-ka/values-ka.xml" qualifiers="ka"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-de/values-de.xml" qualifiers="de"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-as/values-as.xml" qualifiers="as"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"৯৯৯+"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-az/values-az.xml" qualifiers="az"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-ko/values-ko.xml" qualifiers="ko"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-ml/values-ml.xml" qualifiers="ml"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-mk/values-mk.xml" qualifiers="mk"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-kn/values-kn.xml" qualifiers="kn"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-bs/values-bs.xml" qualifiers="bs"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-my/values-my.xml" qualifiers="my"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"၉၉၉+"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-ar/values-ar.xml" qualifiers="ar"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-es-rUS/values-es-rUS.xml" qualifiers="es-rUS"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-gl/values-gl.xml" qualifiers="gl"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">">999"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-pt/values-pt.xml" qualifiers="pt"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-uk/values-uk.xml" qualifiers="uk"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-sr/values-sr.xml" qualifiers="sr"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-pa/values-pa.xml" qualifiers="pa"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-si/values-si.xml" qualifiers="si"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-b+sr+Latn/values-b+sr+Latn.xml" qualifiers="b+sr+Latn"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-en-rXC/values-en-rXC.xml" qualifiers="en-rXC"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"‎‏‎‎‎‎‎‏‎‏‏‏‎‎‎‎‎‎‏‎‎‏‎‎‎‎‏‏‏‏‏‏‏‏‎‏‎‏‏‏‎‎‎‏‏‏‏‎‎‏‎‎‏‏‎‏‎‏‏‎‎‏‎‏‏‎‎‎‏‎‎‎‎‎‎‎‏‎‎‎‎‏‎‏‎‏‎‎‎‎‏‎‎‎‎‎‎999+‎‏‎‎‏‎"</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-zu/values-zu.xml" qualifiers="zu"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/.gradle/caches/transforms-3/3b7b23c7cf9d64f2cdf97830e0eaf3fa/transformed/jetified-lifecycle-viewmodel-savedstate-2.6.1/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1" from-dependency="true" generated-set="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/.gradle/caches/transforms-3/3b7b23c7cf9d64f2cdf97830e0eaf3fa/transformed/jetified-lifecycle-viewmodel-savedstate-2.6.1/res"><file path="/Users/<USER>/.gradle/caches/transforms-3/3b7b23c7cf9d64f2cdf97830e0eaf3fa/transformed/jetified-lifecycle-viewmodel-savedstate-2.6.1/res/values/values.xml" qualifiers=""/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="androidx.lifecycle:lifecycle-livedata-core:2.6.1$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/.gradle/caches/transforms-3/687008004bad3d3378837076129c7833/transformed/lifecycle-livedata-core-2.6.1/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="androidx.lifecycle:lifecycle-livedata-core:2.6.1" from-dependency="true" generated-set="androidx.lifecycle:lifecycle-livedata-core:2.6.1$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/.gradle/caches/transforms-3/687008004bad3d3378837076129c7833/transformed/lifecycle-livedata-core-2.6.1/res"><file path="/Users/<USER>/.gradle/caches/transforms-3/687008004bad3d3378837076129c7833/transformed/lifecycle-livedata-core-2.6.1/res/values/values.xml" qualifiers=""/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="androidx.lifecycle:lifecycle-runtime:2.6.1$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/.gradle/caches/transforms-3/33c479b53b41603cc3192168771adda7/transformed/lifecycle-runtime-2.6.1/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="androidx.lifecycle:lifecycle-runtime:2.6.1" from-dependency="true" generated-set="androidx.lifecycle:lifecycle-runtime:2.6.1$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/.gradle/caches/transforms-3/33c479b53b41603cc3192168771adda7/transformed/lifecycle-runtime-2.6.1/res"><file path="/Users/<USER>/.gradle/caches/transforms-3/33c479b53b41603cc3192168771adda7/transformed/lifecycle-runtime-2.6.1/res/values/values.xml" qualifiers=""><id name="view_tree_lifecycle_owner"/></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="androidx.lifecycle:lifecycle-viewmodel:2.6.1$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/.gradle/caches/transforms-3/29441c4b9970a8bec5ae4c6584bf1466/transformed/lifecycle-viewmodel-2.6.1/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="androidx.lifecycle:lifecycle-viewmodel:2.6.1" from-dependency="true" generated-set="androidx.lifecycle:lifecycle-viewmodel:2.6.1$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/.gradle/caches/transforms-3/29441c4b9970a8bec5ae4c6584bf1466/transformed/lifecycle-viewmodel-2.6.1/res"><file path="/Users/<USER>/.gradle/caches/transforms-3/29441c4b9970a8bec5ae4c6584bf1466/transformed/lifecycle-viewmodel-2.6.1/res/values/values.xml" qualifiers=""><id name="view_tree_view_model_store_owner"/></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="androidx.lifecycle:lifecycle-livedata:2.6.1$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/.gradle/caches/transforms-3/c96a8659546c6e7fe813d23b94cc24a7/transformed/lifecycle-livedata-2.6.1/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="androidx.lifecycle:lifecycle-livedata:2.6.1" from-dependency="true" generated-set="androidx.lifecycle:lifecycle-livedata:2.6.1$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/.gradle/caches/transforms-3/c96a8659546c6e7fe813d23b94cc24a7/transformed/lifecycle-livedata-2.6.1/res"><file path="/Users/<USER>/.gradle/caches/transforms-3/c96a8659546c6e7fe813d23b94cc24a7/transformed/lifecycle-livedata-2.6.1/res/values/values.xml" qualifiers=""/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="androidx.savedstate:savedstate:1.2.1$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/.gradle/caches/transforms-3/72bc74126f9cd461c513810c4d33efc5/transformed/jetified-savedstate-1.2.1/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="androidx.savedstate:savedstate:1.2.1" from-dependency="true" generated-set="androidx.savedstate:savedstate:1.2.1$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/.gradle/caches/transforms-3/72bc74126f9cd461c513810c4d33efc5/transformed/jetified-savedstate-1.2.1/res"><file path="/Users/<USER>/.gradle/caches/transforms-3/72bc74126f9cd461c513810c4d33efc5/transformed/jetified-savedstate-1.2.1/res/values/values.xml" qualifiers=""><id name="view_tree_saved_state_registry_owner"/></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="androidx.activity:activity:1.8.1$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/.gradle/caches/transforms-3/c03fac50118fed4c446fe2dbbb7ccb60/transformed/jetified-activity-1.8.1/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="androidx.activity:activity:1.8.1" from-dependency="true" generated-set="androidx.activity:activity:1.8.1$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/.gradle/caches/transforms-3/c03fac50118fed4c446fe2dbbb7ccb60/transformed/jetified-activity-1.8.1/res"><file path="/Users/<USER>/.gradle/caches/transforms-3/c03fac50118fed4c446fe2dbbb7ccb60/transformed/jetified-activity-1.8.1/res/values/values.xml" qualifiers=""><item name="report_drawn" type="id"/><id name="view_tree_on_back_pressed_dispatcher_owner"/></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="androidx.core:core-splashscreen:1.0.1$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/.gradle/caches/transforms-3/dd89130c4a8ca50a04569d2aa68e9e1e/transformed/jetified-core-splashscreen-1.0.1/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="androidx.core:core-splashscreen:1.0.1" from-dependency="true" generated-set="androidx.core:core-splashscreen:1.0.1$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/.gradle/caches/transforms-3/dd89130c4a8ca50a04569d2aa68e9e1e/transformed/jetified-core-splashscreen-1.0.1/res"><file path="/Users/<USER>/.gradle/caches/transforms-3/dd89130c4a8ca50a04569d2aa68e9e1e/transformed/jetified-core-splashscreen-1.0.1/res/values-v27/values-v27.xml" qualifiers="v27"><style name="Base.Theme.SplashScreen" parent="Base.v27.Theme.SplashScreen"/><style name="Base.Theme.SplashScreen.Light" parent="Base.v27.Theme.SplashScreen.Light"/><style name="Base.v27.Theme.SplashScreen" parent="Base.v21.Theme.SplashScreen">
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
    </style><style name="Base.v27.Theme.SplashScreen.Light" parent="Base.v21.Theme.SplashScreen.Light">
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
    </style></file><file path="/Users/<USER>/.gradle/caches/transforms-3/dd89130c4a8ca50a04569d2aa68e9e1e/transformed/jetified-core-splashscreen-1.0.1/res/values-v29/values-v29.xml" qualifiers="v29"><style name="Base.Theme.SplashScreen" parent="android:Theme.DeviceDefault.DayNight">
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
        <item name="android:enforceStatusBarContrast">false</item>
        <item name="android:enforceNavigationBarContrast">false</item>
    </style><style name="Base.Theme.SplashScreen.Light" parent="Base.Theme.SplashScreen">
    </style></file><file path="/Users/<USER>/.gradle/caches/transforms-3/dd89130c4a8ca50a04569d2aa68e9e1e/transformed/jetified-core-splashscreen-1.0.1/res/values-watch-v20/values-watch-v20.xml" qualifiers="watch-v20"><dimen name="splashscreen_icon_mask_size_no_background">128dp</dimen><dimen name="splashscreen_icon_mask_size_with_background">103dp</dimen><dimen name="splashscreen_icon_mask_stroke_no_background">34dp</dimen><dimen name="splashscreen_icon_mask_stroke_with_background">28dp</dimen><dimen name="splashscreen_icon_size">?splashScreenIconSize</dimen><dimen name="splashscreen_icon_size_no_background">90dp</dimen><dimen name="splashscreen_icon_size_with_background">72dp</dimen><integer name="default_icon_animation_duration">10000</integer></file><file path="/Users/<USER>/.gradle/caches/transforms-3/dd89130c4a8ca50a04569d2aa68e9e1e/transformed/jetified-core-splashscreen-1.0.1/res/values/values.xml" qualifiers=""><attr format="reference" name="postSplashScreenTheme"/><attr format="dimension" name="splashScreenIconSize"/><attr format="reference" name="windowSplashScreenAnimatedIcon"/><attr format="integer" name="windowSplashScreenAnimationDuration"/><attr format="color" name="windowSplashScreenBackground"/><attr format="color" name="windowSplashScreenIconBackgroundColor"/><dimen name="splashscreen_icon_mask_size_no_background">410dp</dimen><dimen name="splashscreen_icon_mask_size_with_background">342dp</dimen><dimen name="splashscreen_icon_mask_stroke_no_background">109dp</dimen><dimen name="splashscreen_icon_mask_stroke_with_background">92dp</dimen><dimen name="splashscreen_icon_size">?splashScreenIconSize</dimen><dimen name="splashscreen_icon_size_no_background">288dp</dimen><dimen name="splashscreen_icon_size_with_background">240dp</dimen><integer name="default_icon_animation_duration">10000</integer><style name="Base.Theme.SplashScreen" parent="Base.v21.Theme.SplashScreen"/><style name="Base.Theme.SplashScreen.DayNight" parent="Base.Theme.SplashScreen.Light"/><style name="Base.Theme.SplashScreen.Light" parent="Base.v21.Theme.SplashScreen.Light"/><style name="Base.v21.Theme.SplashScreen" parent="android:Theme.DeviceDefault.NoActionBar">
    </style><style name="Base.v21.Theme.SplashScreen.Light" parent="android:Theme.DeviceDefault.Light.NoActionBar">
    </style><style name="Theme.SplashScreen" parent="Theme.SplashScreen.Common">
        <item name="postSplashScreenTheme">?android:attr/theme</item>
        <item name="windowSplashScreenAnimationDuration">
            @integer/default_icon_animation_duration
        </item>
        <item name="windowSplashScreenBackground">?android:colorBackground</item>
        <item name="windowSplashScreenAnimatedIcon">@android:drawable/sym_def_app_icon</item>

    </style><style name="Theme.SplashScreen.Common" parent="Base.Theme.SplashScreen.DayNight">
        <item name="android:windowActionBar">false</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowBackground">
            @drawable/compat_splash_screen_no_icon_background
        </item>
        <item name="android:opacity">opaque</item>
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:fitsSystemWindows">false</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="splashScreenIconSize">@dimen/splashscreen_icon_size_no_background</item>
    </style><style name="Theme.SplashScreen.IconBackground" parent="Theme.SplashScreen">
        <item name="android:windowBackground">@drawable/compat_splash_screen</item>
        <item name="splashScreenIconSize">@dimen/splashscreen_icon_size_with_background</item>
    </style></file><file path="/Users/<USER>/.gradle/caches/transforms-3/dd89130c4a8ca50a04569d2aa68e9e1e/transformed/jetified-core-splashscreen-1.0.1/res/values-night-v8/values-night-v8.xml" qualifiers="night-v8"><style name="Base.Theme.SplashScreen.DayNight" parent="Base.Theme.SplashScreen"/></file><file path="/Users/<USER>/.gradle/caches/transforms-3/dd89130c4a8ca50a04569d2aa68e9e1e/transformed/jetified-core-splashscreen-1.0.1/res/values-v31/values-v31.xml" qualifiers="v31"><style name="Theme.SplashScreen" parent="Base.Theme.SplashScreen.DayNight">
        <item name="android:windowSplashScreenAnimatedIcon">?windowSplashScreenAnimatedIcon</item>
        <item name="android:windowSplashScreenBackground">?windowSplashScreenBackground</item>
        <item name="android:windowSplashScreenAnimationDuration">
            ?windowSplashScreenAnimationDuration
        </item>
    </style><style name="Theme.SplashScreen.IconBackground" parent="Theme.SplashScreen">
        <item name="android:windowSplashScreenIconBackgroundColor">
            ?windowSplashScreenIconBackgroundColor
        </item>
    </style></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="androidx.fragment:fragment:1.7.1$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/.gradle/caches/transforms-3/34ec294f6bdde40744063ea74c4e92c4/transformed/fragment-1.7.1/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="androidx.fragment:fragment:1.7.1" from-dependency="true" generated-set="androidx.fragment:fragment:1.7.1$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/.gradle/caches/transforms-3/34ec294f6bdde40744063ea74c4e92c4/transformed/fragment-1.7.1/res"><file path="/Users/<USER>/.gradle/caches/transforms-3/34ec294f6bdde40744063ea74c4e92c4/transformed/fragment-1.7.1/res/values/values.xml" qualifiers=""><item name="fragment_container_view_tag" type="id"/><item name="special_effects_controller_view_tag" type="id"/><item name="visible_removing_fragment_view_tag" type="id"/><declare-styleable name="Fragment">
        <attr name="android:name"/>
        <attr name="android:id"/>
        <attr name="android:tag"/>
    </declare-styleable><declare-styleable name="FragmentContainerView">
        <attr name="android:name"/>
        <attr name="android:tag"/>
    </declare-styleable></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="godot-lib.template_release.aar$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/.gradle/caches/transforms-3/d0bd9ad73902ada629479ce0a98d38a5/transformed/jetified-godot-lib.template_release/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="godot-lib.template_release.aar" from-dependency="true" generated-set="godot-lib.template_release.aar$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/.gradle/caches/transforms-3/d0bd9ad73902ada629479ce0a98d38a5/transformed/jetified-godot-lib.template_release/res"><file path="/Users/<USER>/.gradle/caches/transforms-3/d0bd9ad73902ada629479ce0a98d38a5/transformed/jetified-godot-lib.template_release/res/values/values.xml" qualifiers=""><dimen name="button_height">48dp</dimen><dimen name="button_padding">10dp</dimen><dimen name="dialog_padding_horizontal">16dp</dimen><dimen name="dialog_padding_vertical">8dp</dimen><dimen name="snackbar_bottom_margin">10dp</dimen><dimen name="text_edit_height">48dp</dimen><string name="dialog_ok">OK</string><string name="error_engine_setup_message">Unable to setup the Godot Engine! Aborting…</string><string name="error_missing_vulkan_requirements_message">Warning - this device does not meet the requirements for Vulkan support</string><string name="godot_project_name_string">godot-project-name</string><string name="kilobytes_per_second">%1$s KB/s</string><string name="notification_download_complete">Download complete</string><string name="notification_download_failed">Download unsuccessful</string><string name="state_completed">Download finished</string><string name="state_connecting">Connecting to the download server</string><string name="state_downloading">Downloading resources</string><string name="state_failed">Download failed</string><string name="state_failed_cancelled">Download canceled</string><string name="state_failed_fetching_url">Download failed because the resources could not be found</string><string name="state_failed_sdcard_full">Download failed because the external storage is full</string><string name="state_failed_unlicensed">Download failed because you may not have purchased this app</string><string name="state_fetching_url">Looking for resources to download</string><string name="state_idle">Waiting for download to start</string><string name="state_paused_by_request">Download paused</string><string name="state_paused_network_setup_failure">Download paused. Test a website in browser</string><string name="state_paused_network_unavailable">Download paused because no network is available</string><string name="state_paused_roaming">Download paused because you are roaming</string><string name="state_paused_sdcard_unavailable">Download paused because the external storage is unavailable</string><string name="state_paused_wifi_disabled">Download paused because wifi is disabled</string><string name="state_paused_wifi_unavailable">Download paused because wifi is unavailable</string><string name="state_unknown">Starting…</string><string name="text_button_cancel">Cancel</string><string name="text_button_cancel_verify">Cancel Verification</string><string name="text_button_pause">Pause Download</string><string name="text_button_resume">Resume Download</string><string name="text_button_resume_cellular">Resume download</string><string name="text_button_wifi_settings">Wi-Fi settings</string><string name="text_error_title">Error!</string><string name="text_paused_cellular">Would you like to enable downloading over cellular connections? Depending on your data plan, this may cost you money.</string><string name="text_paused_cellular_2">If you choose not to enable downloading over cellular connections, the download will automatically resume when wi-fi is available.</string><string name="text_validation_complete">XAPK File Validation Complete. Select OK to exit.</string><string name="text_validation_failed">XAPK File Validation Failed.</string><string name="text_verifying_download">Verifying Download</string><string name="time_remaining">Time remaining: %1$s</string><string name="time_remaining_notification">%1$s left</string><style name="ButtonBackground">
        <item name="android:background">@android:color/background_dark</item>
    </style><style name="NotificationText">
        <item name="android:textColor">?android:attr/textColorPrimary</item>
    </style><style name="NotificationTextShadow" parent="NotificationText">
        <item name="android:textColor">?android:attr/textColorPrimary</item>
        <item name="android:shadowColor">@android:color/background_dark</item>
        <item name="android:shadowDx">1.0</item>
        <item name="android:shadowDy">1.0</item>
        <item name="android:shadowRadius">1</item>
    </style><style name="NotificationTitle">
        <item name="android:textColor">?android:attr/textColorPrimary</item>
        <item name="android:textStyle">bold</item>
    </style></file><file path="/Users/<USER>/.gradle/caches/transforms-3/d0bd9ad73902ada629479ce0a98d38a5/transformed/jetified-godot-lib.template_release/res/values-fa/values-fa.xml" qualifiers="fa"><string name="text_button_cancel">انصراف</string><string name="text_button_cancel_verify">انصراف از تایید شدن</string><string name="text_button_pause">توقف دانلود</string><string name="text_button_resume">ادامه دانلود</string><string name="text_button_resume_cellular">ادامه دانلود</string><string name="text_button_wifi_settings">تنظیمات وای-فای</string><string name="text_paused_cellular">آیا می خواهید بر روی اتصال داده همراه دانلود را شروع کنید؟ بر اساس نوع سطح داده شما این ممکن است برای شما هزینه مالی داشته باشد.</string><string name="text_paused_cellular_2">اگر نمی خواهید بر روی اتصال داده همراه دانلود را شروع کنید ، دانلود به صورت خودکار در زمان دسترسی به وای-فای شروع می شود.</string><string name="text_validation_complete">تایید فایل XAPK تکمیل شد.  برای خروج تایید کنید.</string><string name="text_validation_failed">اعتبارسنجی فایل XAPK ناموق.</string><string name="text_verifying_download">درحال تایید دانلود</string></file><file path="/Users/<USER>/.gradle/caches/transforms-3/d0bd9ad73902ada629479ce0a98d38a5/transformed/jetified-godot-lib.template_release/res/values-ko/values-ko.xml" qualifiers="ko"><string name="kilobytes_per_second">%1$s KB/s</string><string name="notification_download_complete">다운로드 완료</string><string name="notification_download_failed">다운로드 실패</string><string name="state_completed">다운로드 종료</string><string name="state_connecting">다운로드 서버에 연결 중</string><string name="state_downloading">다운로드 중</string><string name="state_failed">다운로드 실패</string><string name="state_failed_cancelled">다운로드 취소</string><string name="state_failed_fetching_url">다운로드 항목을 찾을 수 없어 다운로드가 정지 되었습니다.</string><string name="state_failed_sdcard_full">외부 저장소가 가득차서 다운로드가 실패하였습니다.</string><string name="state_failed_unlicensed">이 앱을 구매하지 않아 다운로드가 정지 되었습니다.</string><string name="state_fetching_url">다운로드할 항목을 찾는 중</string><string name="state_idle">다운로드 시작을 기다리는 중</string><string name="state_paused_by_request">다운로드 일시정지</string><string name="state_paused_network_setup_failure">다운로드가 일시정지 되었습니다. 네트워크 연결 상태를 확인하세요.</string><string name="state_paused_network_unavailable">와이파이를 찾을 수 없어 다운로드가 일시정지 되었습니다.</string><string name="state_paused_roaming">로밍 상태이어서 다운로드가 일시정지 되었습니다.</string><string name="state_paused_sdcard_unavailable">외부 저장소를 사용할 수 없어 다운로드가 일시정지 되었습니다.</string><string name="state_paused_wifi_disabled">와이파이가 비활성화 되어 다운로드가 일시정지 되었습니다.</string><string name="state_paused_wifi_unavailable">와이파이가 사용하능하지 않아 다운로드가 일시정지 되었습니다.</string><string name="state_unknown">시작중…</string><string name="text_button_cancel">취소</string><string name="text_button_cancel_verify">파일 확인 취소</string><string name="text_button_pause">다운로드 일시정지</string><string name="text_button_resume">다운로드 계속하기</string><string name="text_button_resume_cellular">다운로드 계속하기</string><string name="text_button_wifi_settings">와이파이 설정</string><string name="text_paused_cellular">모바일 네트워크를 사용하여 다운로드 하시겠습니까? 남은 데이터 사용량에 따라, 요금이 부과될 수 있습니다.</string><string name="text_paused_cellular_2">모바일 네트워크를 사용하여 다운로드 하지 않을 경우, 와이파이 연결이 가능할 때 자동적으로 다운로드가 이루어집니다.</string><string name="text_validation_complete">추가 파일 확인이 완료되었습니다. 확인을 눌러 진행하세요.</string><string name="text_validation_failed">추가 파일 확인에 실패하였습니다.</string><string name="text_verifying_download">다운로드 확인중</string><string name="time_remaining">남은 시간: %1$s</string><string name="time_remaining_notification">%1$s 남음</string></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/workspace/bingo/android/build/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/workspace/bingo/android/build/res"><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-es-rES/godot_project_name_string.xml" qualifiers="es-rES"><string name="godot_project_name_string">Bingo Friends</string></file><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-ro/godot_project_name_string.xml" qualifiers="ro"><string name="godot_project_name_string">Bingo Friends</string></file><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-ru/godot_project_name_string.xml" qualifiers="ru"><string name="godot_project_name_string">Bingo Friends</string></file><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-tl/godot_project_name_string.xml" qualifiers="tl"><string name="godot_project_name_string">Bingo Friends</string></file><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-zh-rTW/godot_project_name_string.xml" qualifiers="zh-rTW"><string name="godot_project_name_string">Bingo Friends</string></file><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-it/godot_project_name_string.xml" qualifiers="it"><string name="godot_project_name_string">Bingo Friends</string></file><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-ca/godot_project_name_string.xml" qualifiers="ca"><string name="godot_project_name_string">Bingo Friends</string></file><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-cs/godot_project_name_string.xml" qualifiers="cs"><string name="godot_project_name_string">Bingo Friends</string></file><file name="icon" path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/mipmap-xxxhdpi-v4/icon.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="icon_monochrome" path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/mipmap-xxxhdpi-v4/icon_monochrome.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="icon_foreground" path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/mipmap-xxxhdpi-v4/icon_foreground.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="icon" path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/mipmap-hdpi-v4/icon.png" qualifiers="hdpi-v4" type="mipmap"/><file name="icon_monochrome" path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/mipmap-hdpi-v4/icon_monochrome.png" qualifiers="hdpi-v4" type="mipmap"/><file name="icon_foreground" path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/mipmap-hdpi-v4/icon_foreground.png" qualifiers="hdpi-v4" type="mipmap"/><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-in/godot_project_name_string.xml" qualifiers="in"><string name="godot_project_name_string">Bingo Friends</string></file><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-ja/godot_project_name_string.xml" qualifiers="ja"><string name="godot_project_name_string">Bingo Friends</string></file><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-el/godot_project_name_string.xml" qualifiers="el"><string name="godot_project_name_string">Bingo Friends</string></file><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-lv/godot_project_name_string.xml" qualifiers="lv"><string name="godot_project_name_string">Bingo Friends</string></file><file name="icon" path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/mipmap-xhdpi-v4/icon.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="icon_monochrome" path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/mipmap-xhdpi-v4/icon_monochrome.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="icon_foreground" path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/mipmap-xhdpi-v4/icon_foreground.png" qualifiers="xhdpi-v4" type="mipmap"/><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-da/godot_project_name_string.xml" qualifiers="da"><string name="godot_project_name_string">Bingo Friends</string></file><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-zh-rHK/godot_project_name_string.xml" qualifiers="zh-rHK"><string name="godot_project_name_string">Bingo Friends</string></file><file name="icon" path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/mipmap-mdpi-v4/icon.png" qualifiers="mdpi-v4" type="mipmap"/><file name="icon_monochrome" path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/mipmap-mdpi-v4/icon_monochrome.png" qualifiers="mdpi-v4" type="mipmap"/><file name="icon_foreground" path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/mipmap-mdpi-v4/icon_foreground.png" qualifiers="mdpi-v4" type="mipmap"/><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values/themes.xml" qualifiers=""><style name="GodotAppMainTheme" parent="@android:style/Theme.DeviceDefault.NoActionBar">
		<item name="android:windowDrawsSystemBarBackgrounds">false</item>
		<item name="android:windowSwipeToDismiss">false</item>
	</style><style name="GodotAppSplashTheme" parent="Theme.SplashScreen">
		
		<item name="android:windowSplashScreenBackground">@mipmap/icon_background</item>

		
		<item name="windowSplashScreenAnimatedIcon">@mipmap/icon_foreground</item>

		
		<item name="postSplashScreenTheme">@style/GodotAppMainTheme</item>
	</style></file><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values/godot_project_name_string.xml" qualifiers=""><string name="godot_project_name_string">Bingo Friends</string></file><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-pl/godot_project_name_string.xml" qualifiers="pl"><string name="godot_project_name_string">Bingo Friends</string></file><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-vi/godot_project_name_string.xml" qualifiers="vi"><string name="godot_project_name_string">Bingo Friends</string></file><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-sv/godot_project_name_string.xml" qualifiers="sv"><string name="godot_project_name_string">Bingo Friends</string></file><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-sl/godot_project_name_string.xml" qualifiers="sl"><string name="godot_project_name_string">Bingo Friends</string></file><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-sk/godot_project_name_string.xml" qualifiers="sk"><string name="godot_project_name_string">Bingo Friends</string></file><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-tr/godot_project_name_string.xml" qualifiers="tr"><string name="godot_project_name_string">Bingo Friends</string></file><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-th/godot_project_name_string.xml" qualifiers="th"><string name="godot_project_name_string">Bingo Friends</string></file><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-en/godot_project_name_string.xml" qualifiers="en"><string name="godot_project_name_string">Bingo Friends</string></file><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-fa/godot_project_name_string.xml" qualifiers="fa"><string name="godot_project_name_string">Bingo Friends</string></file><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-lt/godot_project_name_string.xml" qualifiers="lt"><string name="godot_project_name_string">Bingo Friends</string></file><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-iw/godot_project_name_string.xml" qualifiers="iw"><string name="godot_project_name_string">Bingo Friends</string></file><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-fi/godot_project_name_string.xml" qualifiers="fi"><string name="godot_project_name_string">Bingo Friends</string></file><file name="icon" path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/mipmap/icon.png" qualifiers="" type="mipmap"/><file name="icon_monochrome" path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/mipmap/icon_monochrome.png" qualifiers="" type="mipmap"/><file name="icon_foreground" path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/mipmap/icon_foreground.png" qualifiers="" type="mipmap"/><file name="icon" path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/mipmap-xxhdpi-v4/icon.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="icon_monochrome" path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/mipmap-xxhdpi-v4/icon_monochrome.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="icon_foreground" path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/mipmap-xxhdpi-v4/icon_foreground.png" qualifiers="xxhdpi-v4" type="mipmap"/><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-fr/godot_project_name_string.xml" qualifiers="fr"><string name="godot_project_name_string">Bingo Friends</string></file><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-es/godot_project_name_string.xml" qualifiers="es"><string name="godot_project_name_string">Bingo Friends</string></file><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-hr/godot_project_name_string.xml" qualifiers="hr"><string name="godot_project_name_string">Bingo Friends</string></file><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-hu/godot_project_name_string.xml" qualifiers="hu"><string name="godot_project_name_string">Bingo Friends</string></file><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-nl/godot_project_name_string.xml" qualifiers="nl"><string name="godot_project_name_string">Bingo Friends</string></file><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-bg/godot_project_name_string.xml" qualifiers="bg"><string name="godot_project_name_string">Bingo Friends</string></file><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-nb/godot_project_name_string.xml" qualifiers="nb"><string name="godot_project_name_string">Bingo Friends</string></file><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-hi/godot_project_name_string.xml" qualifiers="hi"><string name="godot_project_name_string">Bingo Friends</string></file><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-de/godot_project_name_string.xml" qualifiers="de"><string name="godot_project_name_string">Bingo Friends</string></file><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-ko/godot_project_name_string.xml" qualifiers="ko"><string name="godot_project_name_string">Bingo Friends</string></file><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-ar/godot_project_name_string.xml" qualifiers="ar"><string name="godot_project_name_string">Bingo Friends</string></file><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-pt/godot_project_name_string.xml" qualifiers="pt"><string name="godot_project_name_string">Bingo Friends</string></file><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-zh/godot_project_name_string.xml" qualifiers="zh"><string name="godot_project_name_string">Bingo Friends</string></file><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-uk/godot_project_name_string.xml" qualifiers="uk"><string name="godot_project_name_string">Bingo Friends</string></file><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-sr/godot_project_name_string.xml" qualifiers="sr"><string name="godot_project_name_string">Bingo Friends</string></file><file name="icon" path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/mipmap-anydpi-v26/icon.xml" qualifiers="anydpi-v26" type="mipmap"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="mono$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/workspace/bingo/android/build/src/mono/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="mono" generated-set="mono$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/workspace/bingo/android/build/src/mono/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/workspace/bingo/android/build/src/release/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release" generated-set="release$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/workspace/bingo/android/build/src/release/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/workspace/bingo/android/build/build/generated/res/resValues/mono/release"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/workspace/bingo/android/build/build/generated/res/resValues/mono/release"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="variant$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/workspace/bingo/android/build/src/monoRelease/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="variant" generated-set="variant$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/workspace/bingo/android/build/src/monoRelease/res"/></dataSet><mergedItems><configuration qualifiers=""><declare-styleable name="FontFamilyFont">
        
        <attr name="fontStyle">
            <enum name="normal" value="0"/>
            <enum name="italic" value="1"/>
        </attr>
        
        <attr format="reference" name="font"/>
        
        <attr format="integer" name="fontWeight"/>
        
        <attr format="string" name="fontVariationSettings"/>
        
        <attr format="integer" name="ttcIndex"/>
        
        <attr name="android:fontStyle"/>
        <attr name="android:font"/>
        <attr name="android:fontWeight"/>
        <attr name="android:fontVariationSettings"/>
        <attr name="android:ttcIndex"/>
    </declare-styleable><declare-styleable name="GradientColorItem">
        
        <attr name="android:offset"/>
        
        <attr name="android:color"/>
    </declare-styleable><declare-styleable name="FontFamily">
        
        <attr format="string" name="fontProviderAuthority"/>
        
        <attr format="string" name="fontProviderPackage"/>
        
        <attr format="string" name="fontProviderQuery"/>
        
        <attr format="reference" name="fontProviderCerts"/>
        
        <attr name="fontProviderFetchStrategy">
            
            <enum name="blocking" value="0"/>
            
            <enum name="async" value="1"/>
        </attr>
        
        <attr format="integer" name="fontProviderFetchTimeout">
            
            <enum name="forever" value="-1"/>
        </attr>
        
        <attr format="string" name="fontProviderSystemFontFamily"/>
    </declare-styleable><declare-styleable name="Capability">
        
        <attr format="reference" name="queryPatterns"/>
        
        <attr format="boolean" name="shortcutMatchRequired"/>
    </declare-styleable><declare-styleable name="GradientColor">
        
        <attr name="android:startColor"/>
        
        <attr name="android:centerColor"/>
        
        <attr name="android:endColor"/>
        
        <attr name="android:type"/>

        
        
        <attr name="android:gradientRadius"/>

        
        
        <attr name="android:centerX"/>
        
        <attr name="android:centerY"/>

        
        
        <attr name="android:startX"/>
        
        <attr name="android:startY"/>
        
        <attr name="android:endX"/>
        
        <attr name="android:endY"/>

        
        <attr name="android:tileMode"/>
    </declare-styleable><declare-styleable name="ColorStateListItem">
        
        <attr name="android:color"/>
        
        <attr format="float" name="alpha"/>
        <attr name="android:alpha"/>
        
        <attr format="float" name="lStar"/>
        <attr name="android:lStar"/>
    </declare-styleable><declare-styleable name="Fragment">
        <attr name="android:name"/>
        <attr name="android:id"/>
        <attr name="android:tag"/>
    </declare-styleable><declare-styleable name="FragmentContainerView">
        <attr name="android:name"/>
        <attr name="android:tag"/>
    </declare-styleable></configuration></mergedItems></merger>