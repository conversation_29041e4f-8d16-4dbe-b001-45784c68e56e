<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/workspace/bingo/android/build/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/workspace/bingo/android/build/res"><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-es-rES/godot_project_name_string.xml" qualifiers="es-rES"><string name="godot_project_name_string">Bingo Friends</string></file><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-ro/godot_project_name_string.xml" qualifiers="ro"><string name="godot_project_name_string">Bingo Friends</string></file><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-ru/godot_project_name_string.xml" qualifiers="ru"><string name="godot_project_name_string">Bingo Friends</string></file><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-tl/godot_project_name_string.xml" qualifiers="tl"><string name="godot_project_name_string">Bingo Friends</string></file><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-zh-rTW/godot_project_name_string.xml" qualifiers="zh-rTW"><string name="godot_project_name_string">Bingo Friends</string></file><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-it/godot_project_name_string.xml" qualifiers="it"><string name="godot_project_name_string">Bingo Friends</string></file><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-ca/godot_project_name_string.xml" qualifiers="ca"><string name="godot_project_name_string">Bingo Friends</string></file><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-cs/godot_project_name_string.xml" qualifiers="cs"><string name="godot_project_name_string">Bingo Friends</string></file><file name="icon" path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/mipmap-xxxhdpi-v4/icon.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="icon_monochrome" path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/mipmap-xxxhdpi-v4/icon_monochrome.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="icon_foreground" path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/mipmap-xxxhdpi-v4/icon_foreground.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="icon" path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/mipmap-hdpi-v4/icon.png" qualifiers="hdpi-v4" type="mipmap"/><file name="icon_monochrome" path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/mipmap-hdpi-v4/icon_monochrome.png" qualifiers="hdpi-v4" type="mipmap"/><file name="icon_foreground" path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/mipmap-hdpi-v4/icon_foreground.png" qualifiers="hdpi-v4" type="mipmap"/><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-in/godot_project_name_string.xml" qualifiers="in"><string name="godot_project_name_string">Bingo Friends</string></file><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-ja/godot_project_name_string.xml" qualifiers="ja"><string name="godot_project_name_string">Bingo Friends</string></file><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-el/godot_project_name_string.xml" qualifiers="el"><string name="godot_project_name_string">Bingo Friends</string></file><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-lv/godot_project_name_string.xml" qualifiers="lv"><string name="godot_project_name_string">Bingo Friends</string></file><file name="icon" path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/mipmap-xhdpi-v4/icon.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="icon_monochrome" path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/mipmap-xhdpi-v4/icon_monochrome.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="icon_foreground" path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/mipmap-xhdpi-v4/icon_foreground.png" qualifiers="xhdpi-v4" type="mipmap"/><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-da/godot_project_name_string.xml" qualifiers="da"><string name="godot_project_name_string">Bingo Friends</string></file><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-zh-rHK/godot_project_name_string.xml" qualifiers="zh-rHK"><string name="godot_project_name_string">Bingo Friends</string></file><file name="icon" path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/mipmap-mdpi-v4/icon.png" qualifiers="mdpi-v4" type="mipmap"/><file name="icon_monochrome" path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/mipmap-mdpi-v4/icon_monochrome.png" qualifiers="mdpi-v4" type="mipmap"/><file name="icon_foreground" path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/mipmap-mdpi-v4/icon_foreground.png" qualifiers="mdpi-v4" type="mipmap"/><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values/themes.xml" qualifiers=""><style name="GodotAppMainTheme" parent="@android:style/Theme.DeviceDefault.NoActionBar">
		<item name="android:windowDrawsSystemBarBackgrounds">false</item>
		<item name="android:windowSwipeToDismiss">false</item>
	</style><style name="GodotAppSplashTheme" parent="Theme.SplashScreen">
		
		<item name="android:windowSplashScreenBackground">@mipmap/icon_background</item>

		
		<item name="windowSplashScreenAnimatedIcon">@mipmap/icon_foreground</item>

		
		<item name="postSplashScreenTheme">@style/GodotAppMainTheme</item>
	</style></file><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values/godot_project_name_string.xml" qualifiers=""><string name="godot_project_name_string">Bingo Friends</string></file><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-pl/godot_project_name_string.xml" qualifiers="pl"><string name="godot_project_name_string">Bingo Friends</string></file><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-vi/godot_project_name_string.xml" qualifiers="vi"><string name="godot_project_name_string">Bingo Friends</string></file><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-sv/godot_project_name_string.xml" qualifiers="sv"><string name="godot_project_name_string">Bingo Friends</string></file><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-sl/godot_project_name_string.xml" qualifiers="sl"><string name="godot_project_name_string">Bingo Friends</string></file><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-sk/godot_project_name_string.xml" qualifiers="sk"><string name="godot_project_name_string">Bingo Friends</string></file><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-tr/godot_project_name_string.xml" qualifiers="tr"><string name="godot_project_name_string">Bingo Friends</string></file><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-th/godot_project_name_string.xml" qualifiers="th"><string name="godot_project_name_string">Bingo Friends</string></file><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-en/godot_project_name_string.xml" qualifiers="en"><string name="godot_project_name_string">Bingo Friends</string></file><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-fa/godot_project_name_string.xml" qualifiers="fa"><string name="godot_project_name_string">Bingo Friends</string></file><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-lt/godot_project_name_string.xml" qualifiers="lt"><string name="godot_project_name_string">Bingo Friends</string></file><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-iw/godot_project_name_string.xml" qualifiers="iw"><string name="godot_project_name_string">Bingo Friends</string></file><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-fi/godot_project_name_string.xml" qualifiers="fi"><string name="godot_project_name_string">Bingo Friends</string></file><file name="icon" path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/mipmap/icon.png" qualifiers="" type="mipmap"/><file name="icon_monochrome" path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/mipmap/icon_monochrome.png" qualifiers="" type="mipmap"/><file name="icon_foreground" path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/mipmap/icon_foreground.png" qualifiers="" type="mipmap"/><file name="icon" path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/mipmap-xxhdpi-v4/icon.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="icon_monochrome" path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/mipmap-xxhdpi-v4/icon_monochrome.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="icon_foreground" path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/mipmap-xxhdpi-v4/icon_foreground.png" qualifiers="xxhdpi-v4" type="mipmap"/><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-fr/godot_project_name_string.xml" qualifiers="fr"><string name="godot_project_name_string">Bingo Friends</string></file><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-es/godot_project_name_string.xml" qualifiers="es"><string name="godot_project_name_string">Bingo Friends</string></file><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-hr/godot_project_name_string.xml" qualifiers="hr"><string name="godot_project_name_string">Bingo Friends</string></file><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-hu/godot_project_name_string.xml" qualifiers="hu"><string name="godot_project_name_string">Bingo Friends</string></file><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-nl/godot_project_name_string.xml" qualifiers="nl"><string name="godot_project_name_string">Bingo Friends</string></file><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-bg/godot_project_name_string.xml" qualifiers="bg"><string name="godot_project_name_string">Bingo Friends</string></file><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-nb/godot_project_name_string.xml" qualifiers="nb"><string name="godot_project_name_string">Bingo Friends</string></file><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-hi/godot_project_name_string.xml" qualifiers="hi"><string name="godot_project_name_string">Bingo Friends</string></file><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-de/godot_project_name_string.xml" qualifiers="de"><string name="godot_project_name_string">Bingo Friends</string></file><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-ko/godot_project_name_string.xml" qualifiers="ko"><string name="godot_project_name_string">Bingo Friends</string></file><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-ar/godot_project_name_string.xml" qualifiers="ar"><string name="godot_project_name_string">Bingo Friends</string></file><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-pt/godot_project_name_string.xml" qualifiers="pt"><string name="godot_project_name_string">Bingo Friends</string></file><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-zh/godot_project_name_string.xml" qualifiers="zh"><string name="godot_project_name_string">Bingo Friends</string></file><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-uk/godot_project_name_string.xml" qualifiers="uk"><string name="godot_project_name_string">Bingo Friends</string></file><file path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-sr/godot_project_name_string.xml" qualifiers="sr"><string name="godot_project_name_string">Bingo Friends</string></file><file name="icon" path="/Users/<USER>/Desktop/workspace/bingo/android/build/res/mipmap-anydpi-v26/icon.xml" qualifiers="anydpi-v26" type="mipmap"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="mono$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/workspace/bingo/android/build/src/mono/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="mono" generated-set="mono$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/workspace/bingo/android/build/src/mono/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/workspace/bingo/android/build/src/release/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release" generated-set="release$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/workspace/bingo/android/build/src/release/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/workspace/bingo/android/build/build/generated/res/resValues/mono/release"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/workspace/bingo/android/build/build/generated/res/resValues/mono/release"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="variant$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/workspace/bingo/android/build/src/monoRelease/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="variant" generated-set="variant$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/workspace/bingo/android/build/src/monoRelease/res"/></dataSet><mergedItems/></merger>