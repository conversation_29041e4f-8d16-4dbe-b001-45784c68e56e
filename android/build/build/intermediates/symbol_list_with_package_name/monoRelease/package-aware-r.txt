com.godot.game
anim fragment_fast_out_extra_slow_in
animator fragment_close_enter
animator fragment_close_exit
animator fragment_fade_enter
animator fragment_fade_exit
animator fragment_open_enter
animator fragment_open_exit
attr alpha
attr font
attr fontProviderAuthority
attr fontProviderCerts
attr fontProviderFetchStrategy
attr fontProviderFetchTimeout
attr fontProviderPackage
attr fontProviderQuery
attr fontProviderSystemFontFamily
attr fontStyle
attr fontVariationSettings
attr fontWeight
attr lStar
attr nestedScrollViewStyle
attr postSplashScreenTheme
attr queryPatterns
attr shortcutMatchRequired
attr splashScreenIconSize
attr ttcIndex
attr windowSplashScreenAnimatedIcon
attr windowSplashScreenAnimationDuration
attr windowSplashScreenBackground
attr windowSplashScreenIconBackgroundColor
color androidx_core_ripple_material_light
color androidx_core_secondary_text_default_material_light
color notification_action_color_filter
color notification_icon_bg_color
dimen button_height
dimen button_padding
dimen compat_button_inset_horizontal_material
dimen compat_button_inset_vertical_material
dimen compat_button_padding_horizontal_material
dimen compat_button_padding_vertical_material
dimen compat_control_corner_material
dimen compat_notification_large_icon_max_height
dimen compat_notification_large_icon_max_width
dimen dialog_padding_horizontal
dimen dialog_padding_vertical
dimen notification_action_icon_size
dimen notification_action_text_size
dimen notification_big_circle_margin
dimen notification_content_margin_start
dimen notification_large_icon_height
dimen notification_large_icon_width
dimen notification_main_column_padding_top
dimen notification_media_narrow_margin
dimen notification_right_icon_size
dimen notification_right_side_padding_top
dimen notification_small_icon_background_padding
dimen notification_small_icon_size_as_large
dimen notification_subtext_size
dimen notification_top_pad
dimen notification_top_pad_large_text
dimen snackbar_bottom_margin
dimen splashscreen_icon_mask_size_no_background
dimen splashscreen_icon_mask_size_with_background
dimen splashscreen_icon_mask_stroke_no_background
dimen splashscreen_icon_mask_stroke_with_background
dimen splashscreen_icon_size
dimen splashscreen_icon_size_no_background
dimen splashscreen_icon_size_with_background
dimen text_edit_height
drawable compat_splash_screen
drawable compat_splash_screen_no_icon_background
drawable icon_background
drawable notification_action_background
drawable notification_bg
drawable notification_bg_low
drawable notification_bg_low_normal
drawable notification_bg_low_pressed
drawable notification_bg_normal
drawable notification_bg_normal_pressed
drawable notification_icon_background
drawable notification_template_icon_bg
drawable notification_template_icon_low_bg
drawable notification_tile_bg
drawable notify_panel_notification_icon_bg
id accessibility_action_clickable_span
id accessibility_custom_action_0
id accessibility_custom_action_1
id accessibility_custom_action_10
id accessibility_custom_action_11
id accessibility_custom_action_12
id accessibility_custom_action_13
id accessibility_custom_action_14
id accessibility_custom_action_15
id accessibility_custom_action_16
id accessibility_custom_action_17
id accessibility_custom_action_18
id accessibility_custom_action_19
id accessibility_custom_action_2
id accessibility_custom_action_20
id accessibility_custom_action_21
id accessibility_custom_action_22
id accessibility_custom_action_23
id accessibility_custom_action_24
id accessibility_custom_action_25
id accessibility_custom_action_26
id accessibility_custom_action_27
id accessibility_custom_action_28
id accessibility_custom_action_29
id accessibility_custom_action_3
id accessibility_custom_action_30
id accessibility_custom_action_31
id accessibility_custom_action_4
id accessibility_custom_action_5
id accessibility_custom_action_6
id accessibility_custom_action_7
id accessibility_custom_action_8
id accessibility_custom_action_9
id action_container
id action_divider
id action_image
id action_text
id actions
id appIcon
id approveCellular
id async
id blocking
id buttonRow
id cancelButton
id chronometer
id description
id dialog_button
id downloadButton
id downloaderDashboard
id forever
id fragment_container_view_tag
id godot_fragment_container
id icon
id icon_group
id info
id italic
id line1
id line3
id normal
id notificationLayout
id notification_background
id notification_main_column
id notification_main_column_container
id pauseButton
id progressAsFraction
id progressAsPercentage
id progressAverageSpeed
id progressBar
id progressTimeRemaining
id progress_bar
id progress_bar_frame
id progress_text
id report_drawn
id resumeOverCellular
id right_icon
id right_side
id snackbar_action
id snackbar_text
id special_effects_controller_view_tag
id splashscreen_icon_view
id statusText
id tag_accessibility_actions
id tag_accessibility_clickable_spans
id tag_accessibility_heading
id tag_accessibility_pane_title
id tag_on_apply_window_listener
id tag_on_receive_content_listener
id tag_on_receive_content_mime_types
id tag_screen_reader_focusable
id tag_state_description
id tag_transition_group
id tag_unhandled_key_event_manager
id tag_unhandled_key_listeners
id tag_window_insets_animation_callback
id text
id text2
id textPausedParagraph1
id textPausedParagraph2
id time
id time_remaining
id title
id view_tree_lifecycle_owner
id view_tree_on_back_pressed_dispatcher_owner
id view_tree_saved_state_registry_owner
id view_tree_view_model_store_owner
id visible_removing_fragment_view_tag
id wifiSettingsButton
integer default_icon_animation_duration
integer status_bar_notification_info_maxnum
layout custom_dialog
layout downloading_expansion
layout godot_app_layout
layout notification_action
layout notification_action_tombstone
layout notification_template_custom_big
layout notification_template_icon_group
layout notification_template_part_chronometer
layout notification_template_part_time
layout snackbar
layout splash_screen_view
layout status_bar_ongoing_event_progress_bar
mipmap icon
mipmap icon_background
mipmap icon_foreground
mipmap icon_monochrome
mipmap themed_icon
string androidx_startup
string dialog_ok
string error_engine_setup_message
string error_missing_vulkan_requirements_message
string godot_project_name_string
string kilobytes_per_second
string notification_download_complete
string notification_download_failed
string state_completed
string state_connecting
string state_downloading
string state_failed
string state_failed_cancelled
string state_failed_fetching_url
string state_failed_sdcard_full
string state_failed_unlicensed
string state_fetching_url
string state_idle
string state_paused_by_request
string state_paused_network_setup_failure
string state_paused_network_unavailable
string state_paused_roaming
string state_paused_sdcard_unavailable
string state_paused_wifi_disabled
string state_paused_wifi_unavailable
string state_unknown
string status_bar_notification_info_overflow
string text_button_cancel
string text_button_cancel_verify
string text_button_pause
string text_button_resume
string text_button_resume_cellular
string text_button_wifi_settings
string text_error_title
string text_paused_cellular
string text_paused_cellular_2
string text_validation_complete
string text_validation_failed
string text_verifying_download
string time_remaining
string time_remaining_notification
style Base_Theme_SplashScreen
style Base_Theme_SplashScreen_DayNight
style Base_Theme_SplashScreen_Light
style Base_v21_Theme_SplashScreen
style Base_v21_Theme_SplashScreen_Light
style Base_v27_Theme_SplashScreen
style Base_v27_Theme_SplashScreen_Light
style ButtonBackground
style GodotAppMainTheme
style GodotAppSplashTheme
style NotificationText
style NotificationTextShadow
style NotificationTitle
style TextAppearance_Compat_Notification
style TextAppearance_Compat_Notification_Info
style TextAppearance_Compat_Notification_Line2
style TextAppearance_Compat_Notification_Time
style TextAppearance_Compat_Notification_Title
style Theme_SplashScreen
style Theme_SplashScreen_Common
style Theme_SplashScreen_IconBackground
style Widget_Compat_NotificationActionContainer
style Widget_Compat_NotificationActionText
styleable Capability queryPatterns shortcutMatchRequired
styleable ColorStateListItem android_color android_alpha android_lStar alpha lStar
styleable FontFamily fontProviderAuthority fontProviderCerts fontProviderFetchStrategy fontProviderFetchTimeout fontProviderPackage fontProviderQuery fontProviderSystemFontFamily
styleable FontFamilyFont android_font android_fontWeight android_fontStyle android_ttcIndex android_fontVariationSettings font fontStyle fontVariationSettings fontWeight ttcIndex
styleable Fragment android_name android_id android_tag
styleable FragmentContainerView android_name android_tag
styleable GradientColor android_startColor android_endColor android_type android_centerX android_centerY android_gradientRadius android_tileMode android_centerColor android_startX android_startY android_endX android_endY
styleable GradientColorItem android_color android_offset
xml godot_provider_paths
