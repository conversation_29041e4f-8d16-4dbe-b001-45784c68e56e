<variant
    name="monoRelease"
    package="com.godot.game"
    minSdkVersion="24"
    targetSdkVersion="34"
    mergedManifest="build/intermediates/merged_manifest/monoRelease/AndroidManifest.xml"
    manifestMergeReport="build/outputs/logs/manifest-merger-mono-release-report.txt"
    proguardFiles="build/intermediates/default_proguard_files/global/proguard-android.txt-8.2.0"
    partialResultsDir="build/intermediates/lint_vital_partial_results/monoRelease/out">
  <buildFeatures
      namespacing="REQUIRED"/>
  <sourceProviders>
    <sourceProvider
        manifests="AndroidManifest.xml:src/release/AndroidManifest.xml"
        javaDirectories="src:src/mono/java:src/release/java:src/monoRelease/java:src/main/kotlin:src/mono/kotlin:src/release/kotlin:src/monoRelease/kotlin"
        resDirectories="res:src/mono/res:src/release/res:src/monoRelease/res"
        assetsDirectories="assets:src/mono/assets:src/release/assets:src/monoRelease/assets"/>
  </sourceProviders>
  <testSourceProviders>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <manifestPlaceholders>
    <placeholder
        name="godotEditorVersion"
        value="4.4.1.stable.mono" />
    <placeholder
        name="godotRenderingMethod"
        value="gl_compatibility" />
  </manifestPlaceholders>
  <artifact
      classOutputs="build/intermediates/javac/monoRelease/classes:build/tmp/kotlin-classes/monoRelease:build/kotlinToolingMetadata:build/intermediates/compile_and_runtime_not_namespaced_r_class_jar/monoRelease/R.jar"
      type="MAIN"
      applicationId="com.bingo.game"
      generatedSourceFolders="build/generated/ap_generated_sources/monoRelease/out:build/generated/source/buildConfig/mono/release"
      generatedResourceFolders="build/generated/res/resValues/mono/release"
      desugaredMethodsFiles="/Users/<USER>/.gradle/caches/transforms-3/d645d716b531e054d573ec1d2c804a7e/transformed/D8BackportedDesugaredMethods.txt">
  </artifact>
</variant>
