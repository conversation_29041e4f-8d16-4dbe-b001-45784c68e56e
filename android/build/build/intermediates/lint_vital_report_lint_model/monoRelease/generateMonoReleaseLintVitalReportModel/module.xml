<lint-module
    format="1"
    dir="/Users/<USER>/Desktop/workspace/bingo/android/build"
    name=":"
    type="APP"
    maven=":build:"
    agpVersion="8.2.0"
    buildFolder="build"
    bootClassPath="/Users/<USER>/Library/Android/sdk/platforms/android-34/android.jar:/Users/<USER>/Library/Android/sdk/build-tools/34.0.0/core-lambda-stubs.jar"
    javaSourceLevel="17"
    compileTarget="android-34"
    neverShrinking="true">
  <lintOptions
      disable="MissingTranslation,UnusedResources"
      absolutePaths="true"
      checkReleaseBuilds="true"
      explainIssues="true">
    <severities>
      <severity
        id="MissingTranslation"
        severity="IGNORE" />
      <severity
        id="UnusedResources"
        severity="IGNORE" />
    </severities>
  </lintOptions>
  <variant name="monoRelease"/>
</lint-module>
