1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.bingo.game"
4    android:installLocation="auto"
5    android:versionCode="1"
6    android:versionName="1.0" >
7
8    <uses-sdk
9        android:minSdkVersion="24"
10        android:targetSdkVersion="34" />
11
12    <supports-screens
12-->/Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:8:5-12:40
13        android:largeScreens="true"
13-->/Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:11:9-36
14        android:normalScreens="true"
14-->/Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:10:9-37
15        android:smallScreens="true"
15-->/Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:9:9-36
16        android:xlargeScreens="true" />
16-->/Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:12:9-37
17
18    <uses-feature
18-->/Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:14:5-16:35
19        android:glEsVersion="0x00030000"
19-->/Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:15:9-41
20        android:required="true" />
20-->/Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:16:9-32
21
22    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
22-->/Users/<USER>/Desktop/workspace/bingo/android/build/src/release/AndroidManifest.xml:11:5-79
22-->/Users/<USER>/Desktop/workspace/bingo/android/build/src/release/AndroidManifest.xml:11:22-76
23    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
23-->/Users/<USER>/Desktop/workspace/bingo/android/build/src/release/AndroidManifest.xml:12:5-76
23-->/Users/<USER>/Desktop/workspace/bingo/android/build/src/release/AndroidManifest.xml:12:22-73
24    <uses-permission android:name="android.permission.CHANGE_WIFI_MULTICAST_STATE" />
24-->/Users/<USER>/Desktop/workspace/bingo/android/build/src/release/AndroidManifest.xml:13:5-86
24-->/Users/<USER>/Desktop/workspace/bingo/android/build/src/release/AndroidManifest.xml:13:22-83
25    <uses-permission android:name="android.permission.INTERNET" />
25-->/Users/<USER>/Desktop/workspace/bingo/android/build/src/release/AndroidManifest.xml:14:5-67
25-->/Users/<USER>/Desktop/workspace/bingo/android/build/src/release/AndroidManifest.xml:14:22-64
26    <uses-permission android:name="android.permission.VIBRATE" />
26-->/Users/<USER>/Desktop/workspace/bingo/android/build/src/release/AndroidManifest.xml:15:5-66
26-->/Users/<USER>/Desktop/workspace/bingo/android/build/src/release/AndroidManifest.xml:15:22-63
27    <uses-permission android:name="android.permission.WAKE_LOCK" />
27-->/Users/<USER>/Desktop/workspace/bingo/android/build/src/release/AndroidManifest.xml:16:5-68
27-->/Users/<USER>/Desktop/workspace/bingo/android/build/src/release/AndroidManifest.xml:16:22-65
28
29    <application
29-->/Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:18:5-60:19
30        android:allowBackup="false"
30-->/Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:20:9-36
31        android:appCategory="game"
31-->/Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:22:9-35
32        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
32-->[androidx.core:core:1.8.0] /Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/AndroidManifest.xml:24:18-86
33        android:extractNativeLibs="false"
34        android:hasFragileUserData="false"
34-->/Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:24:9-43
35        android:icon="@mipmap/icon"
35-->/Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:21:9-36
36        android:isGame="true"
36-->/Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:23:9-30
37        android:label="@string/godot_project_name_string"
37-->/Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:19:9-58
38        android:requestLegacyExternalStorage="false" >
38-->/Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:25:9-53
39        <activity
39-->/Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:41:9-58:20
40            android:name="com.godot.game.GodotApp"
40-->/Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:42:13-37
41            android:configChanges="layoutDirection|locale|orientation|keyboardHidden|screenSize|smallestScreenSize|density|keyboard|navigation|screenLayout|uiMode"
41-->/Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:49:13-164
42            android:excludeFromRecents="false"
42-->/Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:46:13-47
43            android:exported="true"
43-->/Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:47:13-36
44            android:label="@string/godot_project_name_string"
44-->/Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:43:13-62
45            android:launchMode="singleInstancePerTask"
45-->/Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:45:13-55
46            android:resizeableActivity="true"
46-->/Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:50:13-47
47            android:screenOrientation="landscape"
47-->/Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:48:13-50
48            android:theme="@style/GodotAppSplashTheme" >
48-->/Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:44:13-55
49            <intent-filter>
49-->/Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:53:13-57:29
50                <action android:name="android.intent.action.MAIN" />
50-->/Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:54:17-69
50-->/Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:54:25-66
51
52                <category android:name="android.intent.category.DEFAULT" />
52-->/Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:55:17-76
52-->/Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:55:27-73
53                <category android:name="android.intent.category.LAUNCHER" />
53-->/Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:56:17-77
53-->/Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:56:27-74
54            </intent-filter>
55        </activity>
56
57        <profileable
57-->/Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:27:9-30:36
58            android:enabled="true"
58-->/Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:29:13-35
59            android:shell="true" /> <!-- Records the version of the Godot editor used for building -->
59-->/Users/<USER>/Desktop/workspace/bingo/android/build/AndroidManifest.xml:28:13-33
60        <meta-data
61            android:name="org.godotengine.editor.version"
62            android:value="4.4.1.stable.mono" /> <!-- Records the rendering method used by the Godot engine -->
63        <meta-data
64            android:name="org.godotengine.rendering.method"
65            android:value="gl_compatibility" /> <!-- Records the version of the Godot library -->
66        <meta-data
66-->[godot-lib.template_release.aar] /Users/<USER>/.gradle/caches/transforms-3/d0bd9ad73902ada629479ce0a98d38a5/transformed/jetified-godot-lib.template_release/AndroidManifest.xml:12:9-14:44
67            android:name="org.godotengine.library.version"
67-->[godot-lib.template_release.aar] /Users/<USER>/.gradle/caches/transforms-3/d0bd9ad73902ada629479ce0a98d38a5/transformed/jetified-godot-lib.template_release/AndroidManifest.xml:13:13-59
68            android:value="4.4.1.stable" />
68-->[godot-lib.template_release.aar] /Users/<USER>/.gradle/caches/transforms-3/d0bd9ad73902ada629479ce0a98d38a5/transformed/jetified-godot-lib.template_release/AndroidManifest.xml:14:13-41
69
70        <service android:name="org.godotengine.godot.GodotDownloaderService" />
70-->[godot-lib.template_release.aar] /Users/<USER>/.gradle/caches/transforms-3/d0bd9ad73902ada629479ce0a98d38a5/transformed/jetified-godot-lib.template_release/AndroidManifest.xml:16:9-80
70-->[godot-lib.template_release.aar] /Users/<USER>/.gradle/caches/transforms-3/d0bd9ad73902ada629479ce0a98d38a5/transformed/jetified-godot-lib.template_release/AndroidManifest.xml:16:18-77
71
72        <activity
72-->[godot-lib.template_release.aar] /Users/<USER>/.gradle/caches/transforms-3/d0bd9ad73902ada629479ce0a98d38a5/transformed/jetified-godot-lib.template_release/AndroidManifest.xml:18:9-22:75
73            android:name="org.godotengine.godot.utils.ProcessPhoenix"
73-->[godot-lib.template_release.aar] /Users/<USER>/.gradle/caches/transforms-3/d0bd9ad73902ada629479ce0a98d38a5/transformed/jetified-godot-lib.template_release/AndroidManifest.xml:19:13-70
74            android:exported="false"
74-->[godot-lib.template_release.aar] /Users/<USER>/.gradle/caches/transforms-3/d0bd9ad73902ada629479ce0a98d38a5/transformed/jetified-godot-lib.template_release/AndroidManifest.xml:20:13-37
75            android:process=":phoenix"
75-->[godot-lib.template_release.aar] /Users/<USER>/.gradle/caches/transforms-3/d0bd9ad73902ada629479ce0a98d38a5/transformed/jetified-godot-lib.template_release/AndroidManifest.xml:21:13-39
76            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
76-->[godot-lib.template_release.aar] /Users/<USER>/.gradle/caches/transforms-3/d0bd9ad73902ada629479ce0a98d38a5/transformed/jetified-godot-lib.template_release/AndroidManifest.xml:22:13-72
77
78        <provider
78-->[godot-lib.template_release.aar] /Users/<USER>/.gradle/caches/transforms-3/d0bd9ad73902ada629479ce0a98d38a5/transformed/jetified-godot-lib.template_release/AndroidManifest.xml:24:9-32:20
79            android:name="androidx.core.content.FileProvider"
79-->[godot-lib.template_release.aar] /Users/<USER>/.gradle/caches/transforms-3/d0bd9ad73902ada629479ce0a98d38a5/transformed/jetified-godot-lib.template_release/AndroidManifest.xml:25:13-62
80            android:authorities="com.bingo.game.fileprovider"
80-->[godot-lib.template_release.aar] /Users/<USER>/.gradle/caches/transforms-3/d0bd9ad73902ada629479ce0a98d38a5/transformed/jetified-godot-lib.template_release/AndroidManifest.xml:26:13-64
81            android:exported="false"
81-->[godot-lib.template_release.aar] /Users/<USER>/.gradle/caches/transforms-3/d0bd9ad73902ada629479ce0a98d38a5/transformed/jetified-godot-lib.template_release/AndroidManifest.xml:27:13-37
82            android:grantUriPermissions="true" >
82-->[godot-lib.template_release.aar] /Users/<USER>/.gradle/caches/transforms-3/d0bd9ad73902ada629479ce0a98d38a5/transformed/jetified-godot-lib.template_release/AndroidManifest.xml:28:13-47
83            <meta-data
83-->[godot-lib.template_release.aar] /Users/<USER>/.gradle/caches/transforms-3/d0bd9ad73902ada629479ce0a98d38a5/transformed/jetified-godot-lib.template_release/AndroidManifest.xml:29:13-31:64
84                android:name="android.support.FILE_PROVIDER_PATHS"
84-->[godot-lib.template_release.aar] /Users/<USER>/.gradle/caches/transforms-3/d0bd9ad73902ada629479ce0a98d38a5/transformed/jetified-godot-lib.template_release/AndroidManifest.xml:30:17-67
85                android:resource="@xml/godot_provider_paths" />
85-->[godot-lib.template_release.aar] /Users/<USER>/.gradle/caches/transforms-3/d0bd9ad73902ada629479ce0a98d38a5/transformed/jetified-godot-lib.template_release/AndroidManifest.xml:31:17-61
86        </provider>
87        <provider
87-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/618293b988a94b2ad64fc8615e872ffd/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:24:9-32:20
88            android:name="androidx.startup.InitializationProvider"
88-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/618293b988a94b2ad64fc8615e872ffd/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:25:13-67
89            android:authorities="com.bingo.game.androidx-startup"
89-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/618293b988a94b2ad64fc8615e872ffd/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:26:13-68
90            android:exported="false" >
90-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/618293b988a94b2ad64fc8615e872ffd/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:27:13-37
91            <meta-data
91-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/618293b988a94b2ad64fc8615e872ffd/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:29:13-31:52
92                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
92-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/618293b988a94b2ad64fc8615e872ffd/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:30:17-85
93                android:value="androidx.startup" />
93-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/618293b988a94b2ad64fc8615e872ffd/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:31:17-49
94        </provider>
95
96        <receiver
96-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/618293b988a94b2ad64fc8615e872ffd/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:34:9-52:20
97            android:name="androidx.profileinstaller.ProfileInstallReceiver"
97-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/618293b988a94b2ad64fc8615e872ffd/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:35:13-76
98            android:directBootAware="false"
98-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/618293b988a94b2ad64fc8615e872ffd/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:36:13-44
99            android:enabled="true"
99-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/618293b988a94b2ad64fc8615e872ffd/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:37:13-35
100            android:exported="true"
100-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/618293b988a94b2ad64fc8615e872ffd/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:38:13-36
101            android:permission="android.permission.DUMP" >
101-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/618293b988a94b2ad64fc8615e872ffd/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:39:13-57
102            <intent-filter>
102-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/618293b988a94b2ad64fc8615e872ffd/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:40:13-42:29
103                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
103-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/618293b988a94b2ad64fc8615e872ffd/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:41:17-91
103-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/618293b988a94b2ad64fc8615e872ffd/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:41:25-88
104            </intent-filter>
105            <intent-filter>
105-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/618293b988a94b2ad64fc8615e872ffd/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:43:13-45:29
106                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
106-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/618293b988a94b2ad64fc8615e872ffd/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:44:17-85
106-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/618293b988a94b2ad64fc8615e872ffd/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:44:25-82
107            </intent-filter>
108            <intent-filter>
108-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/618293b988a94b2ad64fc8615e872ffd/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:46:13-48:29
109                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
109-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/618293b988a94b2ad64fc8615e872ffd/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:47:17-88
109-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/618293b988a94b2ad64fc8615e872ffd/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:47:25-85
110            </intent-filter>
111            <intent-filter>
111-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/618293b988a94b2ad64fc8615e872ffd/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:49:13-51:29
112                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
112-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/618293b988a94b2ad64fc8615e872ffd/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:50:17-95
112-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/618293b988a94b2ad64fc8615e872ffd/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:50:25-92
113            </intent-filter>
114        </receiver>
115    </application>
116
117</manifest>
