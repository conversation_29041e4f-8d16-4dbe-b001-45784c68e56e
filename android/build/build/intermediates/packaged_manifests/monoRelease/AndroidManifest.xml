<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.bingo.game"
    android:installLocation="auto"
    android:versionCode="1"
    android:versionName="1.0" >

    <uses-sdk
        android:minSdkVersion="24"
        android:targetSdkVersion="34" />

    <supports-screens
        android:largeScreens="true"
        android:normalScreens="true"
        android:smallScreens="true"
        android:xlargeScreens="true" />

    <uses-feature
        android:glEsVersion="0x00030000"
        android:required="true" />

    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_MULTICAST_STATE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />

    <application
        android:allowBackup="false"
        android:appCategory="game"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:extractNativeLibs="false"
        android:hasFragileUserData="false"
        android:icon="@mipmap/icon"
        android:isGame="true"
        android:label="@string/godot_project_name_string"
        android:requestLegacyExternalStorage="false" >
        <activity
            android:name="com.godot.game.GodotApp"
            android:configChanges="layoutDirection|locale|orientation|keyboardHidden|screenSize|smallestScreenSize|density|keyboard|navigation|screenLayout|uiMode"
            android:excludeFromRecents="false"
            android:exported="true"
            android:label="@string/godot_project_name_string"
            android:launchMode="singleInstancePerTask"
            android:resizeableActivity="true"
            android:screenOrientation="landscape"
            android:theme="@style/GodotAppSplashTheme" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <profileable
            android:enabled="true"
            android:shell="true" /> <!-- Records the version of the Godot editor used for building -->
        <meta-data
            android:name="org.godotengine.editor.version"
            android:value="4.4.1.stable.mono" /> <!-- Records the rendering method used by the Godot engine -->
        <meta-data
            android:name="org.godotengine.rendering.method"
            android:value="gl_compatibility" /> <!-- Records the version of the Godot library -->
        <meta-data
            android:name="org.godotengine.library.version"
            android:value="4.4.1.stable" />

        <service android:name="org.godotengine.godot.GodotDownloaderService" />

        <activity
            android:name="org.godotengine.godot.utils.ProcessPhoenix"
            android:exported="false"
            android:process=":phoenix"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="com.bingo.game.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true" >
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/godot_provider_paths" />
        </provider>
        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="com.bingo.game.androidx-startup"
            android:exported="false" >
            <meta-data
                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
                android:value="androidx.startup" />
        </provider>

        <receiver
            android:name="androidx.profileinstaller.ProfileInstallReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.DUMP" >
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
            </intent-filter>
        </receiver>
    </application>

</manifest>