{"logs": [{"outputFile": "com.godot.game-mergeMonoReleaseResources-17:/values-ko/values-ko.xml", "map": [{"source": "/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-ko/godot_project_name_string.xml", "from": {"startLines": "3", "startColumns": "1", "startOffsets": "112", "endColumns": "64", "endOffsets": "175"}, "to": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "67", "endOffsets": "118"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-ko/values-ko.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "24", "startColumns": "4", "startOffsets": "1604", "endColumns": "100", "endOffsets": "1700"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/d0bd9ad73902ada629479ce0a98d38a5/transformed/jetified-godot-lib.template_release/res/values-ko/values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,181,246,298,357,410,459,518,605,686,767,829,884,946,1046,1139,1215,1310,1397,1489,1536,1586,1649,1705,1762,1828,1890,1999,2115,2201,2271,2332,2387", "endColumns": "58,66,64,51,58,52,48,58,86,80,80,61,54,61,99,92,75,94,86,91,46,49,62,55,56,65,61,108,115,85,69,60,54,63", "endOffsets": "109,176,241,293,352,405,454,513,600,681,762,824,879,941,1041,1134,1210,1305,1392,1484,1531,1581,1644,1700,1757,1823,1885,1994,2110,2196,2266,2327,2382,2446"}, "to": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,25,26,27,28,29,30,31,32,33,34,35,36,37", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "123,182,249,314,366,425,478,527,586,673,754,835,897,952,1014,1114,1207,1283,1378,1465,1557,1705,1755,1818,1874,1931,1997,2059,2168,2284,2370,2440,2501,2556", "endColumns": "58,66,64,51,58,52,48,58,86,80,80,61,54,61,99,92,75,94,86,91,46,49,62,55,56,65,61,108,115,85,69,60,54,63", "endOffsets": "177,244,309,361,420,473,522,581,668,749,830,892,947,1009,1109,1202,1278,1373,1460,1552,1599,1750,1813,1869,1926,1992,2054,2163,2279,2365,2435,2496,2551,2615"}}]}]}