{"logs": [{"outputFile": "com.godot.game-mergeMonoReleaseResources-17:/values-lt/values-lt.xml", "map": [{"source": "/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-lt/godot_project_name_string.xml", "from": {"startLines": "3", "startColumns": "1", "startOffsets": "112", "endColumns": "64", "endOffsets": "175"}, "to": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "67", "endOffsets": "118"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-lt/values-lt.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "3", "startColumns": "4", "startOffsets": "123", "endColumns": "100", "endOffsets": "219"}}]}]}