{"logs": [{"outputFile": "com.godot.game-mergeMonoReleaseResources-17:/values/values.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-3/c03fac50118fed4c446fe2dbbb7ccb60/transformed/jetified-activity-1.8.1/res/values/values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "86,105", "startColumns": "4,4", "startOffsets": "5336,6345", "endColumns": "41,59", "endOffsets": "5373,6400"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/9e5db9b26d9392a98e8f1e9350d92494/transformed/jetified-startup-runtime-1.1.1/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "111", "startColumns": "4", "startOffsets": "6711", "endColumns": "82", "endOffsets": "6789"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/29441c4b9970a8bec5ae4c6584bf1466/transformed/lifecycle-viewmodel-2.6.1/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "107", "startColumns": "4", "startOffsets": "6459", "endColumns": "49", "endOffsets": "6504"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/dd89130c4a8ca50a04569d2aa68e9e1e/transformed/jetified-core-splashscreen-1.0.1/res/values/values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,21,23,32,45", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,174,243,315,378,450,524,600,676,753,824,893,964,1032,1113,1205,1298,1407,1528,1988,2763", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,20,22,31,44,48", "endColumns": "59,58,68,71,62,71,73,75,75,76,70,68,70,67,80,91,92,12,12,12,12,12", "endOffsets": "110,169,238,310,373,445,519,595,671,748,819,888,959,1027,1108,1200,1293,1402,1523,1983,2758,3031"}, "to": {"startLines": "3,4,5,6,7,8,40,41,42,43,44,45,46,109,152,153,154,155,157,201,210,223", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "115,175,234,303,375,438,2524,2598,2674,2750,2827,2898,2967,6573,10164,10245,10337,10430,10539,12780,13240,14015", "endLines": "3,4,5,6,7,8,40,41,42,43,44,45,46,109,152,153,154,156,158,209,222,226", "endColumns": "59,58,68,71,62,71,73,75,75,76,70,68,70,67,80,91,92,12,12,12,12,12", "endOffsets": "170,229,298,370,433,505,2593,2669,2745,2822,2893,2962,3033,6636,10240,10332,10425,10534,10655,13235,14010,14283"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/34ec294f6bdde40744063ea74c4e92c4/transformed/fragment-1.7.1/res/values/values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "83,87,108,299,304", "startColumns": "4,4,4,4,4", "startOffsets": "5209,5378,6509,17343,17513", "endLines": "83,87,108,303,307", "endColumns": "56,64,63,24,24", "endOffsets": "5261,5438,6568,17508,17657"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values/values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,89,90,94,95,96,97,103,113,146,167,200", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,344,407,477,545,617,687,748,822,895,956,1017,1079,1143,1205,1266,1334,1434,1494,1560,1633,1702,1759,1811,1873,1945,2021,2086,2145,2204,2264,2324,2384,2444,2504,2564,2624,2684,2744,2804,2863,2923,2983,3043,3103,3163,3223,3283,3343,3403,3463,3522,3582,3642,3701,3760,3819,3878,3937,3996,4031,4066,4121,4184,4239,4297,4355,4416,4479,4536,4587,4637,4698,4755,4821,4855,4890,4925,4995,5066,5183,5384,5494,5695,5824,5896,5963,6166,6467,8198,8879,9561", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,88,89,93,94,95,96,102,112,145,166,199,205", "endColumns": "59,71,87,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,339,402,472,540,612,682,743,817,890,951,1012,1074,1138,1200,1261,1329,1429,1489,1555,1628,1697,1754,1806,1868,1940,2016,2081,2140,2199,2259,2319,2379,2439,2499,2559,2619,2679,2739,2799,2858,2918,2978,3038,3098,3158,3218,3278,3338,3398,3458,3517,3577,3637,3696,3755,3814,3873,3932,3991,4026,4061,4116,4179,4234,4292,4350,4411,4474,4531,4582,4632,4693,4750,4816,4850,4885,4920,4990,5061,5178,5379,5489,5690,5819,5891,5958,6161,6462,8193,8874,9556,9723"}, "to": {"startLines": "2,9,10,11,12,15,16,17,18,19,20,21,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,84,85,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,110,137,190,191,195,196,200,227,228,229,235,245,278,308,341", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,510,582,670,739,893,963,1031,1103,1173,1234,1308,1492,1553,1614,1676,1740,1802,1863,1931,2031,2091,2157,2230,2299,2356,2408,3086,3158,3234,3299,3358,3417,3477,3537,3597,3657,3717,3777,3837,3897,3957,4017,4076,4136,4196,4256,4316,4376,4436,4496,4556,4616,4676,4735,4795,4855,4914,4973,5032,5091,5150,5266,5301,5443,5498,5561,5616,5674,5732,5793,5856,5913,5964,6014,6075,6132,6198,6232,6267,6641,8915,12022,12139,12340,12450,12651,14288,14360,14427,14630,14931,16662,17662,18344", "endLines": "2,9,10,11,12,15,16,17,18,19,20,21,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,84,85,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,110,137,190,194,195,199,200,227,228,234,244,277,298,340,346", "endColumns": "59,71,87,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,577,665,734,797,958,1026,1098,1168,1229,1303,1376,1548,1609,1671,1735,1797,1858,1926,2026,2086,2152,2225,2294,2351,2403,2465,3153,3229,3294,3353,3412,3472,3532,3592,3652,3712,3772,3832,3892,3952,4012,4071,4131,4191,4251,4311,4371,4431,4491,4551,4611,4671,4730,4790,4850,4909,4968,5027,5086,5145,5204,5296,5331,5493,5556,5611,5669,5727,5788,5851,5908,5959,6009,6070,6127,6193,6227,6262,6297,6706,8981,12134,12335,12445,12646,12775,14355,14422,14625,14926,16657,17338,18339,18506"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/33c479b53b41603cc3192168771adda7/transformed/lifecycle-runtime-2.6.1/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "104", "startColumns": "4", "startOffsets": "6302", "endColumns": "42", "endOffsets": "6340"}}, {"source": "/Users/<USER>/Desktop/workspace/bingo/android/build/res/values/themes.xml", "from": {"startLines": "3,8", "startColumns": "1,1", "startOffsets": "53,279", "endLines": "6,20", "endColumns": "9,9", "endOffsets": "276,891"}, "to": {"startLines": "162,166", "startColumns": "4,4", "startOffsets": "10787,11015", "endLines": "165,175", "endColumns": "9,9", "endOffsets": "11010,11327"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/d0bd9ad73902ada629479ce0a98d38a5/transformed/jetified-godot-lib.template_release/res/values/values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,50,53,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,100,146,203,257,311,359,400,499,714,773,850,929,991,1070,1138,1195,1264,1375,1481,1593,1674,1743,1811,1917,2026,2115,2235,2331,2433,2485,2539,2613,2674,2737,2809,2878,2930,3097,3279,3382,3462,3533,3597,3663,3790,3916,4302", "endLines": "2,3,4,5,6,7,8,9,10,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,52,59,63", "endColumns": "44,45,56,53,53,47,40,98,141,58,76,78,61,78,67,56,68,110,105,111,80,68,67,105,108,88,119,95,101,51,53,73,60,62,71,68,51,166,181,102,79,70,63,65,12,12,12,12", "endOffsets": "95,141,198,252,306,354,395,494,636,768,845,924,986,1065,1133,1190,1259,1370,1476,1588,1669,1738,1806,1912,2021,2110,2230,2326,2428,2480,2534,2608,2669,2732,2804,2873,2925,3092,3274,3377,3457,3528,3592,3658,3785,3911,4297,4475"}, "to": {"startLines": "13,14,22,23,39,47,112,113,114,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,138,139,140,141,142,143,144,145,146,147,148,149,150,151,159,176,179,186", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "802,847,1381,1438,2470,3038,6794,6835,6934,7144,7203,7280,7359,7421,7500,7568,7625,7694,7805,7911,8023,8104,8173,8241,8347,8456,8545,8665,8761,8863,8986,9040,9114,9175,9238,9310,9379,9431,9598,9780,9883,9963,10034,10098,10660,11332,11458,11844", "endLines": "13,14,22,23,39,47,112,113,114,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,138,139,140,141,142,143,144,145,146,147,148,149,150,151,161,178,185,189", "endColumns": "44,45,56,53,53,47,40,98,141,58,76,78,61,78,67,56,68,110,105,111,80,68,67,105,108,88,119,95,101,51,53,73,60,62,71,68,51,166,181,102,79,70,63,65,12,12,12,12", "endOffsets": "842,888,1433,1487,2519,3081,6830,6929,7071,7198,7275,7354,7416,7495,7563,7620,7689,7800,7906,8018,8099,8168,8236,8342,8451,8540,8660,8756,8858,8910,9035,9109,9170,9233,9305,9374,9426,9593,9775,9878,9958,10029,10093,10159,10782,11453,11839,12017"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/72bc74126f9cd461c513810c4d33efc5/transformed/jetified-savedstate-1.2.1/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "106", "startColumns": "4", "startOffsets": "6405", "endColumns": "53", "endOffsets": "6454"}}, {"source": "/Users/<USER>/Desktop/workspace/bingo/android/build/res/values/godot_project_name_string.xml", "from": {"startLines": "3", "startColumns": "1", "startOffsets": "112", "endColumns": "64", "endOffsets": "175"}, "to": {"startLines": "115", "startColumns": "4", "startOffsets": "7076", "endColumns": "67", "endOffsets": "7139"}}]}]}