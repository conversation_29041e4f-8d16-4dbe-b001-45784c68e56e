{"logs": [{"outputFile": "com.godot.game-mergeMonoReleaseResources-17:/values-fa/values-fa.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-3/245f1aa0863a677574781ac0a1900f6e/transformed/core-1.8.0/res/values-fa/values-fa.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "3", "startColumns": "4", "startOffsets": "123", "endColumns": "100", "endOffsets": "219"}}, {"source": "/Users/<USER>/Desktop/workspace/bingo/android/build/res/values-fa/godot_project_name_string.xml", "from": {"startLines": "3", "startColumns": "1", "startOffsets": "112", "endColumns": "64", "endOffsets": "175"}, "to": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "67", "endOffsets": "118"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/d0bd9ad73902ada629479ce0a98d38a5/transformed/jetified-godot-lib.template_release/res/values-fa/values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,109,183,241,301,370,440,618,791,893,972", "endColumns": "53,73,57,59,68,69,177,172,101,78,70", "endOffsets": "104,178,236,296,365,435,613,786,888,967,1038"}, "to": {"startLines": "4,5,6,7,8,9,10,11,12,13,14", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "224,278,352,410,470,539,609,787,960,1062,1141", "endColumns": "53,73,57,59,68,69,177,172,101,78,70", "endOffsets": "273,347,405,465,534,604,782,955,1057,1136,1207"}}]}]}