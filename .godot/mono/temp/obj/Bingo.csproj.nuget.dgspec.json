{"format": 1, "restore": {"/Users/<USER>/Desktop/workspace/bingo/Bingo.csproj": {}}, "projects": {"/Users/<USER>/Desktop/workspace/bingo/Bingo.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Desktop/workspace/bingo/Bingo.csproj", "projectName": "<PERSON><PERSON>", "projectPath": "/Users/<USER>/Desktop/workspace/bingo/Bingo.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Desktop/workspace/bingo/.godot/mono/temp/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Godot.SourceGenerators": {"target": "Package", "version": "[4.4.1, )"}, "GodotSharp": {"target": "Package", "version": "[4.4.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.NETCore.App.Runtime.Mono.android-arm64", "version": "[8.0.15, 8.0.15]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/8.0.408/PortableRuntimeIdentifierGraph.json"}}, "runtimes": {"android-arm64": {"#import": []}}}}}