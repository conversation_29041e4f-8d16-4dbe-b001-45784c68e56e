is_global = true
build_property.GodotDisabledSourceGenerators = 
build_property.GodotProjectDir = /Users/<USER>/Desktop/workspace/bingo/
build_property.GodotProjectDirBase64 = 
build_property.GodotSourceGenerators = 
build_property.IsGodotToolsProject = 
build_property.TargetFramework = net8.0
build_property.TargetPlatformMinVersion = 
build_property.UsingMicrosoftNETSdkWeb = 
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.RootNamespace = Bingo
build_property.ProjectDir = /Users/<USER>/Desktop/workspace/bingo/
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
