{"version": 3, "targets": {"net8.0": {"Godot.SourceGenerators/4.4.1": {"type": "package", "build": {"build/Godot.SourceGenerators.props": {}}}, "GodotSharp/4.4.1": {"type": "package", "compile": {"lib/net8.0/GodotSharp.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/GodotSharp.dll": {"related": ".xml"}}}}, "net8.0/android-arm64": {"Godot.SourceGenerators/4.4.1": {"type": "package", "build": {"build/Godot.SourceGenerators.props": {}}}, "GodotSharp/4.4.1": {"type": "package", "compile": {"lib/net8.0/GodotSharp.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/GodotSharp.dll": {"related": ".xml"}}}}}, "libraries": {"Godot.SourceGenerators/4.4.1": {"sha512": "V/cuX41BxippWGD79zrP2bhqfXkuyiy9OFuCkTu3flo7I6STSJca637TL2phe7rzROIFre0vQR1+PAMdsjO3zg==", "type": "package", "path": "godot.sourcegenerators/4.4.1", "files": [".nupkg.metadata", ".signature.p7s", "analyzers/dotnet/cs/Godot.SourceGenerators.dll", "build/Godot.SourceGenerators.props", "godot.sourcegenerators.4.4.1.nupkg.sha512", "godot.sourcegenerators.nuspec"]}, "GodotSharp/4.4.1": {"sha512": "ghnQEo5LikQPfbCYcVxje8epffNCiyNG4zvGWUDRZRC1O+653+yqG3wdxk3+5RZsA3jaRuGKRavsGcnhLKe12g==", "type": "package", "path": "godotsharp/4.4.1", "files": [".nupkg.metadata", ".signature.p7s", "godotsharp.4.4.1.nupkg.sha512", "godotsharp.nuspec", "lib/net8.0/GodotSharp.dll", "lib/net8.0/GodotSharp.xml"]}}, "projectFileDependencyGroups": {"net8.0": ["Godot.SourceGenerators >= 4.4.1", "GodotSharp >= 4.4.1"]}, "packageFolders": {"/Users/<USER>/.nuget/packages/": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Desktop/workspace/bingo/Bingo.csproj", "projectName": "<PERSON><PERSON>", "projectPath": "/Users/<USER>/Desktop/workspace/bingo/Bingo.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Desktop/workspace/bingo/.godot/mono/temp/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Godot.SourceGenerators": {"target": "Package", "version": "[4.4.1, )"}, "GodotSharp": {"target": "Package", "version": "[4.4.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.NETCore.App.Runtime.Mono.android-arm64", "version": "[8.0.15, 8.0.15]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/8.0.408/PortableRuntimeIdentifierGraph.json"}}, "runtimes": {"android-arm64": {"#import": []}}}}