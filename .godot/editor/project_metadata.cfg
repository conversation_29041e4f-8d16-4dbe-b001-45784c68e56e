[editor_metadata]

executable_path="/private/var/folders/vk/ckx3stkj5wn_nlqk16pdjc_h0000gn/T/AppTranslocation/B821CEB3-3BEB-4042-9DE1-A8C35121176A/d/Godot_mono.app/Contents/MacOS/Godot"

[recent_files]

scenes=["res://party_mode.tscn", "res://main_menu.tscn", "res://game_root.tscn"]
scripts=["res://SelfMode.cs", "res://PartyMode.cs"]

[debug_options]

run_main_feature_tags=""
multiple_instances_enabled=true
run_instances_config=Array[Dictionary]([{
"arguments": "",
"features": "",
"override_args": false,
"override_features": false
}, {
"arguments": "",
"features": "",
"override_args": false,
"override_features": false
}])
run_instance_count=2.0

[export_options]

default_filename="Bingo"
export_debug=true

[dialog_bounds]

export=Rect2(1660, 940, 1800, 1000)
